package org.jeecg.modules.crmfy.crmcustomer.vo;

import java.io.Serializable;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CrmCustomerVO  implements Serializable {/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @ApiModelProperty(value = "客户唯一标识")
	private java.lang.Integer id;

	/**客户编码*/
	@Excel(name = "客户编码", width = 15)
    @ApiModelProperty(value = "客户编码")
	private java.lang.String customerNo;

	/**客户姓名*/
	@Excel(name = "客户姓名", width = 15)
    @ApiModelProperty(value = "客户姓名")
	private java.lang.String customerName;
	/**姓*/
	@Excel(name = "姓", width = 15)
    @ApiModelProperty(value = "姓")
	private java.lang.String lastNameCn;
	/**名*/
	@Excel(name = "名", width = 15)
    @ApiModelProperty(value = "名")
	private java.lang.String firstNameCn;
	/**英文姓*/
	@Excel(name = "英文姓", width = 15)
    @ApiModelProperty(value = "英文姓")
	private java.lang.String lastName;
	/**英文名*/
	@Excel(name = "英文名", width = 15)
    @ApiModelProperty(value = "英文名")
	private java.lang.String firstName;
	/**性别 0-未知 1-男 2-女*/
	@Excel(name = "性别 0-未知 1-男 2-女", width = 15)
    @ApiModelProperty(value = "性别 0-未知 1-男 2-女")
	private java.lang.Integer gender;
	/**出生日期*/
	@Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
	private java.util.Date birthDate;

		/**国籍*/
	@Excel(name = "国籍", width = 15)
    @ApiModelProperty(value = "国籍")
	private java.lang.String nationality;
	
	/**证件号*/
	@Excel(name = "证件号", width = 15)
    @ApiModelProperty(value = "证件号")
	private java.lang.String idNumber;
	/**证件类型 1身份证，2护照，3其他*/
	@Excel(name = "证件类型 1身份证，2护照，3其他", width = 15)
    @ApiModelProperty(value = "证件类型 1身份证，2护照，3其他")
	private java.lang.String idType;
	/**证件有效期起始*/
	@Excel(name = "证件有效期起始", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "证件有效期起始")
	private java.util.Date idStartDate;
	/**证件有效期截止*/
	@Excel(name = "证件有效期截止", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "证件有效期截止")
	private java.util.Date idEndDate;
	/**身高cm*/
	@Excel(name = "身高cm", width = 15)
    @ApiModelProperty(value = "身高cm")
	private java.math.BigDecimal high;
	/**体重kg*/
	@Excel(name = "体重kg", width = 15)
    @ApiModelProperty(value = "体重kg")
	private java.math.BigDecimal weight;
	/**是否吸烟1是 0否*/
	@Excel(name = "是否吸烟1是 0否", width = 15)
    @ApiModelProperty(value = "是否吸烟1是 0否")
	private java.lang.String izSmoking;
	/**国际码*/
	@Excel(name = "国际码", width = 15)
    @ApiModelProperty(value = "国际码")
	private java.lang.String callingCode;

	/** 手机号码 */
	@Excel(name = "手机号码", width = 15)
	@ApiModelProperty(value = "手机号码")
	private java.lang.String mobile;
	/** 电子邮箱 */
	@Excel(name = "电子邮箱", width = 15)
	@ApiModelProperty(value = "电子邮箱")
	private java.lang.String email;
/**是否已婚1是 0否*/
	@Excel(name = "是否已婚1是 0否", width = 15)
    @ApiModelProperty(value = "是否已婚1是 0否")
	private java.lang.String izMarried;
	/**学历*/
	@Excel(name = "学历", width = 15)
    @ApiModelProperty(value = "学历")
	private java.lang.String education;
	/**户籍地址*/
	@Excel(name = "户籍地址", width = 15)
    @ApiModelProperty(value = "户籍地址")
	private java.lang.String idAddress;
	/**通讯地址*/
	@Excel(name = "通讯地址", width = 15)
    @ApiModelProperty(value = "通讯地址")
	private java.lang.String contactAddress;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
	private java.lang.String address;
	/**工作单位*/
	@Excel(name = "工作单位", width = 15)
    @ApiModelProperty(value = "工作单位")
	private java.lang.String companyName;
	/**工作单位地址*/
	@Excel(name = "工作单位地址", width = 15)
    @ApiModelProperty(value = "工作单位地址")
	private java.lang.String companyAddress;
	/**工作岗位*/
	@Excel(name = "工作岗位", width = 15)
    @ApiModelProperty(value = "工作岗位")
	private java.lang.String occupation;
	/**工作内容*/
	@Excel(name = "工作内容", width = 15)
    @ApiModelProperty(value = "工作内容")
	private java.lang.String workDetail;
	/**年薪(人民币)*/
	@Excel(name = "年薪(人民币)", width = 15)
    @ApiModelProperty(value = "年薪(人民币)")
	private java.lang.String annualIncome;
	/**存款*/
	@Excel(name = "存款", width = 15)
    @ApiModelProperty(value = "存款")
	private java.lang.String deposit;
	/**股票*/
	@Excel(name = "股票", width = 15)
    @ApiModelProperty(value = "股票")
	private java.lang.String stock;
	/**房产*/
	@Excel(name = "房产", width = 15)
    @ApiModelProperty(value = "房产")
	private java.lang.String house;
	/**古玩字画*/
	@Excel(name = "古玩字画", width = 15)
    @ApiModelProperty(value = "古玩字画")
	private java.lang.String oldThing;
	/**资产*/
	@Excel(name = "资产", width = 15)
    @ApiModelProperty(value = "其他资产")
	private java.lang.String otherMoney;
	/**职业信息*/
//	@Excel(name = "职业信息", width = 15)
//    @ApiModelProperty(value = "职业信息")
//	private java.lang.String occupation;
	/**年收入*/
//	@Excel(name = "年收入", width = 15)
//    @ApiModelProperty(value = "年收入")
//	private java.math.BigDecimal annualIncome;
	/**客户区域*/
//	@Excel(name = "客户区域", width = 15)
//    @ApiModelProperty(value = "客户区域")
//	private java.lang.String customerSource;
	/**1-潜在客户 2-意向客户 3-已投保 4-失效客户*/
//	@Excel(name = "1-潜在客户 2-意向客户 3-已投保 4-失效客户", width = 15)
//    @ApiModelProperty(value = "1-潜在客户 2-意向客户 3-已投保 4-失效客户")
//	private java.lang.String customerStatus;
	/**风险承受评估结果*/
//	@Excel(name = "风险承受评估结果", width = 15)
//    @ApiModelProperty(value = "风险承受评估结果")
//	private java.lang.String riskAssessment;
	
	
//	@Excel(name = "转介人", width = 15)
    @ApiModelProperty(value = "转介人账号")
	private java.lang.String username;
    
    @ApiModelProperty(value = "转介人")
	private java.lang.String realname;
	@Dict(dictTable = "crm_customer_grade_config",dicText = "level_name",dicCode = "id")
	@Excel(name = "客户等级", dictTable = "crm_customer_grade_config",dicText = "level_name",dicCode = "id",width = 15)
    @ApiModelProperty(value = "客户等级id")
	private java.lang.String cusLevel;

	/**客户来源*/
	@Dict(dicCode = "customer_from")
	@Excel(name = "客户来源", width = 15,dicCode = "customer_from")
    @ApiModelProperty(value = "客户来源")
	private java.lang.String customerFrom;

	@Dict(dicCode = "customer_source")
	@Excel(name = "客户区域", dicCode = "customer_source",width = 15)
	@ApiModelProperty(value = "客户区域")
	private java.lang.String customerSource;

	
	
	
	
	
	

}
