import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { queryGradeList } from '/@/api/crmfy/crmCustomerGradeConfig.api';

// 列表页表格配置
export const columns: BasicColumn[] = [
  {
    title: '客户编号',
    dataIndex: 'customerNo',
    width: 120,
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'gender',
    width: 80,
    customRender: ({ text }) => {
      const genderMap = {
        0: '未知',
        1: '男',
        2: '女',
      };
      return genderMap[text] || '未知';
    },
  },
  {
    title: '手机号码',
    dataIndex: 'mobile',
    width: 120,
  },
  {
    title: '证件号',
    dataIndex: 'idNumber',
    width: 150,
  },
  {
    title: '证件类型',
    dataIndex: 'idType',
    width: 100,
    customRender: ({ text }) => {
      const typeMap = {
        '1': '身份证',
        '2': '护照',
        '3': '其他',
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '客户等级',
    dataIndex: 'cusLevel_dictText',
    width: 100,
  },
  {
    title: '出生日期',
    dataIndex: 'birthDate',
    width: 120,
  },
  {
    title: '电子邮箱',
    dataIndex: 'email',
    width: 150,
  },
  {
    title: '工作岗位',
    dataIndex: 'occupation',
    width: 120,
  },
  {
    title: '工作单位',
    dataIndex: 'companyName',
    width: 150,
  },
  {
    title: '年薪(人民币)',
    dataIndex: 'annualIncome',
    width: 120,
  },
  // {
  //   title: '客户状态',
  //   dataIndex: 'customerStatus',
  //   width: 120,
  //   customRender: ({ text }) => {
  //     const statusMap = {
  //       '2': '潜在客户',
  //       '0': '意向客户',
  //       '1': '已投保',
  //       '4': '失效客户',
  //     };
  //     return statusMap[text] || text;
  //   },
  // },
  {
    title: '客户来源',
    dataIndex: 'customerFrom_dictText',
    width: 120,
  },
  {
    title: '客户区域',
    dataIndex: 'customerSource_dictText',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
];

// 查询表单配置
export const searchFormSchema: FormSchema[] = [
  {
    label: '客户姓名',
    field: 'customerName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '手机号码',
    field: 'mobile',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '证件号',
    field: 'idNumber',
    component: 'Input',
    colProps: { span: 6 },
  },
  // {
  //   label: '客户状态',
  //   field: 'customerStatus',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '意向客户', value: '0' },
  //       { label: '已投保', value: '1' },
  //       { label: '潜在客户', value: '2' },
  //       { label: '失效客户', value: '3' },
  //     ],
  //   },
  //   colProps: { span: 6 },
  // },
  {
    label: '客户来源',
    field: 'customerFrom',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_from',
    },
    colProps: { span: 6 },
  },
  {
    label: '客户等级',
    field: 'cusLevel',
    component: 'ApiSelect',
    componentProps: {
      api: queryGradeList,
      labelField: 'levelName',
      valueField: 'id',
      immediate: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
];

// 表单配置 - 基本信息
export const formSchema: FormSchema[] = [
  {
    label: 'id',
    field: 'id',
    component: 'Input',
    show: false,
  },
  // ==================== 基本信息 ====================
  {
    label: '',
    field: 'basicInfo',
    component: 'Divider',
    componentProps: {
      orientation: 'left',
    },
    slot: '基本信息',
  },
  {
    label: '客户编号',
    field: 'customerNo',
    component: 'Input',
    dynamicDisabled: true,
    colProps: { span: 12 },
  },

  {
    label: '姓',
    field: 'lastNameCn',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    label: '名',
    field: 'firstNameCn',
    component: 'Input',
    colProps: { span: 12 },
  },
    {
    label: '客户姓名',
    field: 'customerName',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    dynamicDisabled: true,
    show:false,

    //todo.. 不可编辑，由 姓+名 自动填写
  },

    {
    label: '英文姓',
    field: 'lastName',
    component: 'Input',
    colProps: { span: 12 },
    dynamicDisabled: true,
  },
  {
    label: '英文名',
    field: 'firstName',
    component: 'Input',
    colProps: { span: 12 },
    dynamicDisabled: true,
  },

  {
    label: '性别',
    field: 'gender',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未知', value: 0 },
        { label: '男', value: 1 },
        { label: '女', value: 2 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 12 },
  },

  {
    label: '是否已婚',
    field: 'izMarried',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: '0' },
        { label: '是', value: '1' },
      ],
    },
    defaultValue: '0',
    colProps: { span: 12 },
  },
  {
    label: '身高(cm)',
    field: 'high',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      max: 300,
      precision: 1,
    },
    colProps: { span: 12 },
  },
  {
    label: '体重(kg)',
    field: 'weight',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      max: 500,
      precision: 1,
    },
    colProps: { span: 12 },
  },
  {
    label: '是否吸烟',
    field: 'izSmoking',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: '0' },
        { label: '是', value: '1' },
      ],
    },
    defaultValue: '0',
    colProps: { span: 12 },
  },
  {
    label: '学历',
    field: 'education',
    component: 'Input',
    colProps: { span: 12 },
  },
  
  // ==================== 证件信息 ====================
  {
    label: '',
    field: 'idInfo',
    component: 'Divider',
    componentProps: {
      orientation: 'left',
    },
    slot: '证件信息',
  },
  {
    label: '证件类型',
    field: 'idType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'id_type',
    },
    defaultValue: '1',
    colProps: { span: 12 },
  },
  {
    label: '证件号',
    field: 'idNumber',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
  },
  {
    label: '证件有效期起始',
    field: 'idStartDate',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 12 },
  },
  {
    label: '证件有效期截止',
    field: 'idEndDate',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 12 },
  },

  {
    label: '出生日期',
    field: 'birthDate',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 12 },
  },
  
  // ==================== 联系信息 ====================
  {
    label: '',
    field: 'contactInfo',
    component: 'Divider',
    componentProps: {
      orientation: 'left',
    },
    slot: '联系信息',
  },
  {
    label: '国际码',
    field: 'callingCode',
    component: 'Input',
    componentProps: {
      placeholder: '+86',
    },
    defaultValue: '+86',
    colProps: { span: 12 },
  },
  {
    label: '手机号码',
    field: 'mobile',
    component: 'Input',
    required: true,
    rules: [{ ...rules.phone }],
    colProps: { span: 12 },
  },
  {
    label: '电子邮箱',
    field: 'email',
    component: 'Input',
    rules: [
      { 
        ...rules.email,
        message: '请输入正确的电子邮箱地址',
      },
      {
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        message: '请输入有效的电子邮箱地址',
      },
    ],
    colProps: { span: 12 },
  },
  {
    label: '户籍地址',
    field: 'idAddress',
    component: 'InputTextArea',
    componentProps: {
      rows: 2,
    },
    colProps: { span: 12 },
  },
  {
    label: '通讯地址',
    field: 'contactAddress',
    component: 'InputTextArea',
    componentProps: {
      rows: 2,
    },
    colProps: { span: 12 },
  },
  // {
  //   label: '联系地址',
  //   field: 'address',
  //   component: 'InputTextArea',
  //   componentProps: {
  //     rows: 2,
  //   },
  //   colProps: { span: 12 },
  // },
  
  // ==================== 工作信息 ====================
  {
    label: '',
    field: 'workInfo',
    component: 'Divider',
    componentProps: {
      orientation: 'left',
    },
    slot: '工作信息',
  },
  {
    label: '工作单位',
    field: 'companyName',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    label: '工作岗位',
    field: 'occupation',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    label: '工作单位地址',
    field: 'companyAddress',
    component: 'InputTextArea',
    componentProps: {
      rows: 2,
    },
    colProps: { span: 12 },
  },
  {
    label: '工作内容',
    field: 'workDetail',
    component: 'InputTextArea',
    componentProps: {
      rows: 2,
    },
    colProps: { span: 12 },
  },
  {
    label: '年薪(人民币)',
    field: 'annualIncome',
    component: 'Input',
    componentProps: {
      placeholder: '请输入年薪金额',
      addonAfter: '元',
    },
    colProps: { span: 12 },
  },
];

// 资产情况表单配置
export const assetFormSchema: FormSchema[] = [
  // ==================== 资产情况 ====================
  {
    label: '',
    field: 'assetInfo',
    component: 'Divider',
    componentProps: {
      orientation: 'left',
    },
    slot: '资产情况',
  },
  {
    label: '银行存款',
    field: 'deposit',
    component: 'Input',
    componentProps: {
      placeholder: '请输入银行存款金额',
      addonAfter: '元',
    },
    colProps: { span: 12 },
  },
  {
    label: '股市',
    field: 'stock',
    component: 'Input',
    componentProps: {
      placeholder: '请输入股票投资金额',
      addonAfter: '元',
    },
    colProps: { span: 12 },
  },
  {
    label: '房产',
    field: 'house',
    component: 'Input',
    componentProps: {
      placeholder: '请输入房产价值',
      addonAfter: '元',
    },
    colProps: { span: 12 },
  },
  {
    label: '古玩字画',
    field: 'oldThing',
    component: 'Input',
    componentProps: {
      placeholder: '请输入古玩字画价值',
      addonAfter: '元',
    },
    colProps: { span: 12 },
  },
  {
    label: '其他资产',
    field: 'otherMoney',
    component: 'InputTextArea',
    componentProps: {
      rows: 2,
      placeholder: '请详细描述其他资产情况',
    },
    colProps: { span: 12 },
  },
];

// 客户管理信息表单配置
export const customerManageFormSchema: FormSchema[] = [
  // ==================== 客户管理信息 ====================
  {
    label: '',
    field: 'manageInfo',
    component: 'Divider',
    componentProps: {
      orientation: 'left',
    },
    slot: '客户管理信息',
  },
  {
    label: '客户来源',
    field: 'customerFrom',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_from',
    },
    colProps: { span: 12 },
  },
  {
    label: '客户区域',
    field: 'customerSource',
    component: 'JDictSelectTag',
     componentProps: {
      dictCode: 'customer_source',
    },
    colProps: { span: 12 },
  },
  // {
  //   label: '客户状态',
  //   field: 'customerStatus',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '意向客户', value: '0' },
  //       { label: '已投保', value: '1' },
  //       { label: '潜在客户', value: '2' },
  //       { label: '失效客户', value: '3' },
  //     ],
  //   },
  //   defaultValue: '1',
  //   colProps: { span: 12 },
  // },
  {
    label: '客户等级',
    field: 'cusLevel',
    component: 'ApiSelect',
    componentProps: {
      api: queryGradeList,
      labelField: 'levelName',
      valueField: 'id',
      immediate: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
    ifShow: ({ values }) => {
      // 当编辑时显示
      return values.id;
    },
    dynamicDisabled: true,
    colProps: { span: 12 },
  },
  {
    label: '客户消费累计',
    field: 'cusFee',
    component: 'Input',
    componentProps: {
      placeholder: '请输入客户消费累计金额',
      addonAfter: '美元',
    },
    colProps: { span: 12 },
    dynamicDisabled: true,

  },
  // {
  //   label: '风险承受评估结果',
  //   field: 'riskAssessment',
  //   component: 'InputTextArea',
  //   componentProps: {
  //     rows: 2,
  //     placeholder: '请输入风险承受评估结果',
  //   },
  //   colProps: { span: 12 },
  // },
];
