<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="800" :showOkBtn="false">
    <template #footer>
      <div style="text-align: right;">
        <a-button @click="handleCloseModal">取消</a-button>
        <!-- 发送签字邮件按钮 - 仅在保单已签发且有签字链接时显示 -->
        <a-button
          v-if="showSendEmailButton"
          type="default"
          style="margin-left: 8px;"
          @click="handleSendSignatureEmail"
          :loading="emailLoading"
        >
          发送签字邮件
        </a-button>
        <!-- 保存草稿按钮仅在新增或销售员编辑时显示 -->
        <a-button
          v-if="showSaveDraftButton"
          type="primary"
          style="margin-left: 8px;"
          @click="handleSaveDraft"
          :loading="confirmLoading"
        >
          保存预约
        </a-button>
        <a-button type="primary" style="margin-left: 8px;" @click="handleSubmit" :loading="confirmLoading">提交</a-button>
      </div>
    </template>
    <a-tabs>
      <a-tab-pane key="1" tab="订单信息">
        <BasicForm @register="registerForm">
          <template #productId="{ model, field }">
            <JSelectProduct
              v-model:value="model[field]"
              :supplierField="'supplierNo'"
              :supplierNo="getFieldsValue().supplierNo"
              :productTypeField="'productType'"
              :productType="getFieldsValue().productType"
              @product-selected="handleProductSelected"
            />
          </template>

          <template #customerId="{ model, field }">
            <JSelectCustomer
              v-model:value="model[field]"
              @change="handleCustomerChange"
            />
          </template>

          <template #additionalInsurance>
            <div>
              <a-button type="primary" @click="handleAddAdditionalInsurance" style="margin-bottom: 8px">
                添加附加险
              </a-button>
              <a-table
                :columns="additionalColumns"
                :dataSource="additionalInsuranceList"
                :pagination="false"
                size="small"
                rowKey="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'action'">
                    <a-button type="link" @click="handleEditAdditionalInsurance(record)">编辑</a-button>
                    <a-button type="link" danger @click="handleRemoveAdditionalInsurance(record)">删除</a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </template>
          <template #policyHolderInfo>
            <a-button type="primary" @click="handleEditPolicyHolder">
              {{ getPolicyHolderButtonText() }}
            </a-button>
            <div v-if="policyHolderInfo" style="margin-top: 8px">
              <a-descriptions :column="2" size="small" bordered>
                <a-descriptions-item label="姓名">{{ policyHolderInfo.name }}</a-descriptions-item>
                <a-descriptions-item label="证件号码">{{ policyHolderInfo.idNumber }}</a-descriptions-item>
                <a-descriptions-item label="手机号码">
                  <template v-if="policyHolderInfo.callingCode">
                    {{ policyHolderInfo.callingCode }} → {{ policyHolderInfo.mobile }}
                  </template>
                  <template v-else>
                    {{ policyHolderInfo.mobile }}
                  </template>
                </a-descriptions-item>
                <a-descriptions-item label="电子邮箱">{{ policyHolderInfo.email }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </template>
          <template #insuredInfo>
            <a-button type="primary" @click="handleEditInsured">
              {{ insuredInfo ? '编辑被保人信息' : '添加被保人信息' }}
            </a-button>
            <div v-if="insuredInfo" style="margin-top: 8px">
              <a-descriptions :column="2" size="small" bordered>
                <a-descriptions-item label="姓名">{{ insuredInfo.name }}</a-descriptions-item>
                <a-descriptions-item label="证件号码">{{ insuredInfo.idNumber }}</a-descriptions-item>
                <a-descriptions-item label="手机号码">
                  <template v-if="insuredInfo.callingCode">
                    {{ insuredInfo.callingCode }} → {{ insuredInfo.mobile }}
                  </template>
                  <template v-else>
                    {{ insuredInfo.mobile }}
                  </template>
                </a-descriptions-item>
                <a-descriptions-item label="电子邮箱">{{ insuredInfo.email }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </template>
          <template #beneficiaryInfo>
            <div>
              <a-button type="primary" @click="handleAddBeneficiary" style="margin-bottom: 8px">
                添加受益人信息
              </a-button>
              <a-table
                :columns="beneficiaryColumns"
                :dataSource="beneficiaryList"
                :pagination="false"
                size="small"
                rowKey="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'action'">
                    <a-button type="link" @click="handleEditBeneficiary(record)">编辑</a-button>
                    <a-button type="link" danger @click="handleRemoveBeneficiary(record)">删除</a-button>
                  </template>
                </template>
              </a-table>
              <div v-if="beneficiaryRatioError" style="color: #ff4d4f; margin-top: 8px;">
                受益人受益比例总和必须为100%，当前总和: {{ totalBeneficiaryRatio }}%
              </div>
            </div>
          </template>
          
          <template #policyFiles>
            <div class="policy-files">
              <a-upload-dragger
                :multiple="true"
                :showUploadList="false"
                :beforeUpload="beforeUpload"
                :action="uploadUrl"
                :headers="getHeaders()"
                @change="handleUploadChange"
              >
                <p class="ant-upload-drag-icon">
                  <Icon icon="ant-design:cloud-upload-outlined" />
                </p>
                <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p class="ant-upload-hint">支持单个或批量上传，单个文件不超过20MB</p>
              </a-upload-dragger>
              
              <div v-if="fileList.length > 0" style="margin-top: 16px;">
                <a-table
                  :dataSource="fileList"
                  :columns="fileColumns"
                  :pagination="false"
                  size="small"
                  rowKey="uid"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'fileType'">
                      <a-select 
                        v-model:value="record.fileTypeCode" 
                        placeholder="请选择文件类型"
                        style="width: 100%"
                        @change="(value) => handleFileTypeChange(record, value)"
                      >
                        <a-select-option v-for="item in fileTypeOptions" :key="item.value" :value="item.value">
                          {{ item.text }}
                        </a-select-option>
                      </a-select>
                    </template>
                    <template v-if="column.key === 'action'">
                      <a-button type="link" @click="handleDownloadFile(record)">查看</a-button>
                      <a-button type="link" danger @click="handleRemoveFile(record)">删除</a-button>
                    </template>
                  </template>
                </a-table>
              </div>
            </div>
          </template>
        </BasicForm>

        <!-- 签字链接显示 - 仅在保单已签发且有签字链接时显示 -->
        <div v-if="currentFormData?.signatureSms" style="margin-top: 16px; padding: 16px; border: 1px solid #d9d9d9; border-radius: 6px;">
          <h4 style="margin-bottom: 16px;">保单签字链接</h4>
          <a-form-item >
            <div style="display: flex; align-items: center; gap: 8px;">
              <a-input
                :value="currentFormData?.signatureSms || ''"
                readonly
                style="flex: 1;"
              />
              <a-button
                type="primary"
                size="small"
                @click="copySignatureLink"
              >
                复制链接
              </a-button>
              <a-button
                type="default"
                size="small"
                @click="openSignatureLink"
              >
                打开链接
              </a-button>
            </div>
          </a-form-item>
          <div  v-if="currentFormData?.signatureStatus === '0'" style="margin-top: 12px; padding: 12px; background-color: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px;">
            <p style="margin: 0; color: #52c41a; font-size: 14px;">
              <Icon icon="ant-design:check-circle-outlined" style="margin-right: 8px;" />
              保单已签发，可以发送签字链接给客户进行电子签字确认
            </p>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </BasicModal>

  <AdditionalInsuranceModal @register="registerAdditionalModal" @success="handleAdditionalInsuranceSuccess" />
  <PersonInfoModal @register="registerPersonInfoModal" @success="handlePersonInfoSuccess" />
</template>

<script lang="ts" setup>
  import { ref, computed, unref, watch, nextTick, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import JSelectProduct from '/@/components/Form/src/jeecg/components/JSelectProduct.vue';
  import JSelectCustomer from '/@/components/Form/src/jeecg/components/JSelectCustomer.vue';
  import { policyFormSchema } from '../CrmPolicies.data';
  import { saveOrUpdate, getPolicyDetail, operationAudit, underwritingReply,noticeReply, feesArrival, policyIssuance, policyComplete,policyEdit, sendSignatureEmail } from '/@/api/crmfy/crmPolicies.api';
  import { getProductDetail } from '/@/api/crmfy/crmProducts.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import AdditionalInsuranceModal from './AdditionalInsuranceModal.vue';
  import PersonInfoModal from './PersonInfoModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { getDictItemsByCode, initDictOptions, getDictText, useGlobalDictRefreshKey } from '/@/utils/dict/index';
  import { queryById } from '../../crmcustomer/CrmCustomer.api';
  import { Icon } from '/@/components/Icon';
  // import { uploadFile } from '/@/api/common/api';
  import { useGlobSetting } from '/@/hooks/setting';
  import { getToken } from '/@/utils/auth';
// import { json } from 'stream/consumers';
  //import { v4 as uuidv4 } from 'uuid';

  // 声明Emits
  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();
  const glob = useGlobSetting();
  
  // 上传URL和请求头
  const uploadUrl = computed(() => `${glob.uploadUrl}/sys/common/upload`);
  
  // 获取请求头，包含token
  function getHeaders() {
    return {
      'X-Access-Token': getToken()
    };
  }

  // 获取当前用户信息
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  // 使用全局字典翻译函数
  const globalDictRefreshKey = useGlobalDictRefreshKey();

  // 判断用户角色
  const isSalesRole = computed(() => (userInfo as any)?.postCode === 'customerManager'); // 转介人
    const isYygRole = computed(() => (userInfo as any)?.postCode === 'operationalPost'); // 运营岗

  // 表单状态
  const isUpdate = ref(true);
  const record = ref<Recordable>({});
  const actionType = ref('');
  const confirmLoading = ref(false);
  const emailLoading = ref(false);

  // 判断是否显示保存草稿按钮 - 仅在新增或销售员编辑时显示
  const showSaveDraftButton = computed(() => {
    // 新增操作时显示
    if (!unref(isUpdate)) {
      return true;
    }

    // 销售员编辑操作时显示
    if (unref(isUpdate) && unref(isSalesRole) && !unref(actionType)) {
      return true;
    }

    return false;
  });

  // 自定义关闭弹窗函数
  function handleCloseModal() {
    console.log('关闭弹窗，重置初始化标志');
    modalInitialized.value = false;
    closeModal();
  }

  // 判断是否显示发送签字邮件按钮
  const showSendEmailButton = computed(() => {

    console.log("isYygRole---："+isYygRole);
    // 只有运营岗可以发送邮件
    if (!unref(isYygRole)) {
      console.log("不是运营岗");
      return false;
    }

    // 必须是编辑模式
    if (!unref(isUpdate)) {
      return false;
    }

    // 保单状态必须是已签发(7)且有签字链接且签字状态不为已确认(1)
    const formData  = unref(currentFormData); 
    return formData?.policyStatus === '7' && formData?.signatureSms && formData?.signatureStatus === '0';
  });

  // 附加险相关
  const additionalInsuranceList = ref<any[]>([]);
  const [registerAdditionalModal, { openModal: openAdditionalModal }] = useModal();

  // 附加险表格列定义
  const additionalColumns = [
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '产品名称(英文)',
      dataIndex: 'planName',
      key: 'planName',
    },
    {
      title: '每期保费',
      dataIndex: 'additionalAmount',
      key: 'additionalAmount',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 个人信息相关
  const customerInfo = ref<Recordable | null>(null);
  const policyHolderInfo = ref<Recordable | null>(null);
  const insuredInfo = ref<Recordable | null>(null);
  // 修改为受益人列表
  const beneficiaryList = ref<Recordable[]>([]);
  const beneficiaryRatioError = ref(false);
  const totalBeneficiaryRatio = computed(() => {
    return beneficiaryList.value.reduce((sum, item) => sum + Number(item.benefitRatio || 0), 0);
  });
  
  // 受益人表格列定义
  const beneficiaryColumns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '与被保人关系',
      dataIndex: 'withRelation_dictText',
      key: 'withRelation',
    },
    {
      title: '受益比例(%)',
      dataIndex: 'benefitRatio',
      key: 'benefitRatio',
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      key: 'idNumber',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];
  
  const [registerPersonInfoModal, { openModal: openPersonInfoModal }] = useModal();

  const title = computed(() => {
    if (unref(actionType)) {
      switch (unref(actionType)) {
        case 'operationAudit':
          return '审核提交';
        case 'underwritingReply':
          return '核保回复';
        case 'noticeReply':
          return '照会回复';
        case 'feesArrival':
          return '保费到账';
        case 'policyIssuance':
          return '保单签发';
        case 'policyComplete':
          return '订单完成';
        case 'policyEdit':
          return '编辑';
        default:
          return '';
      }
    }
    return !unref(isUpdate) ? '新增预约' : '编辑预约';
  });

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: policyFormSchema,
    showActionButtonGroup: false,
    // 设置基础列配置为两列布局
    baseColProps: { span: 12 },
    // 设置行属性
    rowProps: { gutter: 24 },

    
  });

  // 弹窗初始化完成标志
  const modalInitialized = ref(false);

  // 获取当前表单数据的计算属性
  const currentFormData = computed(() => {
    try {
      // 只有在弹窗初始化完成后才获取表单数据
      if (!modalInitialized.value) {
        console.log('弹窗还未初始化完成，返回空对象');
        return {};
      }
      
      console.log('获取表单数据开始------');
      // 确保表单已经注册并且 getFieldsValue 方法可用
      if (typeof getFieldsValue === 'function') {
        const formData = getFieldsValue();
        console.log('获取到的表单数据:', formData);
        return formData || {};
      } else {
        console.warn('getFieldsValue 方法不可用，表单可能还未初始化');
        return {};
      }
    } catch (error) {
      console.error('获取表单数据失败:', error);
      return {};
    }
  });

  // 产品详情缓存
  const productDetailCache = ref<Record<string | number, any>>({});

  // 监听供应商变化，确保产品下拉框能正确响应
  watch(
    () => {
      try {
        const formValues = getFieldsValue() || {};
      const supplierNo = formValues.supplierNo ?? '';
        console.log('监听到供应商变化, 当前值:', supplierNo);
        return supplierNo;
      } catch (error) {
        console.error('获取供应商编码失败:', error);
        return '';
      }
    },
    (val) => {
      try {
        console.log('供应商变化处理函数被调用, 新值:', val);
        if (val) {
          // 供应商变化时，确保产品下拉框能正确响应
          nextTick(() => {
            try {
              console.log('供应商变化后的nextTick被调用');
              // 如果是编辑模式且有产品ID，不需要清空
              if (!unref(isUpdate) || !getFieldsValue().productId) {
                console.log('清空产品字段');
                setFieldsValue({
                  productId: undefined,
                  productName: '',
                  planName: '',
                });

                // 清空产品相关选项
                clearProductRelatedOptions();
              }

              // 强制刷新表单，确保供应商编码被正确传递
              nextTick(() => {
                console.log('刷新后的表单值:', getFieldsValue());
              });
            } catch (error) {
              console.error('设置产品字段失败:', error);
            }
          });
        }
      } catch (error) {
        console.error('监听供应商变化处理失败:', error);
      }
    }
  );

  // 监听产品类型变化
  watch(
    () => {
      try {
        return getFieldsValue()?.productType;
      } catch (error) {
        console.error('获取产品类型失败:', error);
        return '';
      }
    },
    (val) => {
      try {
        console.log('产品类型变化处理函数被调用, 新值:', val);
        if (val) {
          // 产品类型变化时，确保产品下拉框能正确响应
          nextTick(() => {
            try {
              console.log('产品类型变化后的nextTick被调用');
              // 如果是编辑模式且有产品ID，不需要清空
              if (!unref(isUpdate) || !getFieldsValue().productId) {
                console.log('清空产品字段');
                setFieldsValue({
                  productId: undefined,
                  productName: '',
                  planName: '',
                });

                // 清空产品相关选项
                clearProductRelatedOptions();
              }

              // 强制刷新表单，确保产品类型被正确传递
              nextTick(() => {
                console.log('刷新后的表单值:', getFieldsValue());
              });
            } catch (error) {
              console.error('设置产品字段失败:', error);
            }
          });
        }
      } catch (error) {
        console.error('监听产品类型变化处理失败:', error);
      }
    }
  );

  // 监听客户ID变化
  watch(
    () => {
      try {
        return getFieldsValue()?.customerId;
      } catch (error) {
        console.error('获取客户ID失败:', error);
        return '';
      }
    },
    async (val) => {
      try {
        console.log('客户ID变化处理函数被调用, 新值:', val);
        if (val) {
          // 获取客户详情
          const customerDetail = await queryById({ id: val });
          console.log('获取到的客户详情:', customerDetail);
          console.log('客户区域值:', customerDetail.customerSource, '类型:', typeof customerDetail.customerSource);
          
          // 设置客户区域到表单
          if (customerDetail && customerDetail.customerSource) {
            setFieldsValue({
              customerSource: customerDetail.customerSource
            });
            console.log('设置客户区域成功:', customerDetail.customerSource);
            
            // 立即更新缴费频率
            if (customerDetail.customerSource === '1') {
              console.log('检测到大陆客户，立即设置年缴');
              updateSchema([
                {
                  field: 'paymentFrequency',
                  componentProps: {
                    placeholder: '大陆客户只能选择年缴',
                    options: [{ label: '年缴', value: '4' }],
                    disabled: false,
                  },
                }
              ]);
              
              setFieldsValue({
                paymentFrequency: '4'
              });
            }
          }
        }
      } catch (error) {
        console.error('监听客户ID变化处理失败:', error);
      }
    }
  );

  // 监听客户区域变化
  watch(
    () => {
      try {
        return getFieldsValue()?.customerSource;
      } catch (error) {
        console.error('获取客户区域失败:', error);
        return '';
      }
    },
    (val, oldVal) => {
      try {
        console.log('客户区域变化处理函数被调用, 新值:', val, '旧值:', oldVal, '类型:', typeof val);
        if (val && val !== oldVal) {
          // 获取当前表单值
          const formValues = getFieldsValue();
          const productId = formValues.productId;

          // 强制转换为字符串并比较
          const customerSource = String(val);
          console.log('转换后的客户区域:', customerSource, '是否等于"1":', customerSource === '1');
          
          if (customerSource === '1') {
            // 大陆客户，强制设置为年缴
            console.log('大陆客户，设置为年缴');
            updateSchema([
              {
                field: 'paymentFrequency',
                componentProps: {
                  placeholder: '大陆客户只能选择年缴',
                  options: [{ label: '年缴', value: '4' }],
                  disabled: false,
                },
              }
            ]);

            // 强制设置为年缴
            setTimeout(() => {
              setFieldsValue({
                paymentFrequency: '4'
              });
              console.log('已设置缴费频率为年缴(4)');
            }, 100);
          } else if (productId) {
            // 非大陆客户且已选产品，恢复产品支持的缴费频率
            console.log('非大陆客户且已选产品，重新加载产品相关选项');
            updateProductRelatedOptions(productId);
          } else {
            // 非大陆客户但未选产品
            console.log('非大陆客户且未选产品，清空相关选项');
            clearProductRelatedOptions();
          }
        }
      } catch (error) {
        console.error('监听客户区域变化处理失败:', error);
      }
    }
  );

  // 获取投保人按钮文本
  function getPolicyHolderButtonText() {
    const formValues = getFieldsValue();
    const isNewCustomer = formValues.isNewCustomer;
    
    if (isNewCustomer) {
      return policyHolderInfo.value ? '编辑投保人' : '添加投保人';
    } else {
      return '编辑投保人';
    }
  }

  // 处理产品选择事件
  async function handleProductSelected(product: any) {
    try {
      if (product) {
        setFieldsValue({
          productName: product.productName || '',
          planName: product.planName || '',
        });

        // 获取产品详情以获取支持的选项
        await updateProductRelatedOptions(product.id);
      } else {
        // 清空产品相关选项
        clearProductRelatedOptions();
      }
    } catch (error) {
      console.error('处理产品选择事件失败:', error);
    }
  }

  // 处理客户选择事件
  async function handleCustomerChange(customerId: string | number) {
    try {
      console.log('客户选择事件被触发, 客户ID:', customerId);
      if (customerId) {
        // 获取客户详情
        const customerDetail = await queryById({ id: customerId });
        console.log('获取到的客户详情:', customerDetail);
        console.log('客户区域值:', customerDetail.customerSource, '类型:', typeof customerDetail.customerSource);
        // 存储客户信息
        customerInfo.value = { ...customerDetail };

        // 检查是否为新客户模式
        const formValues = getFieldsValue();
        const isNewCustomer = formValues.isNewCustomer;
        
        // 如果不是新客户模式，自动复制客户信息到投保人信息
        if (!isNewCustomer) {
          policyHolderInfo.value = {
            // 基本信息
            lastNameCn: customerDetail.lastNameCn,
            firstNameCn: customerDetail.firstNameCn,
            lastName: customerDetail.lastName,
            firstName: customerDetail.firstName,
            name: customerDetail.customerName,
            nameEng: customerDetail.lastName + ' ' + customerDetail.firstName,
            namePy: '', // 拼音会自动生成
            nationality: customerDetail.nationality,
            gender: customerDetail.gender,
            birthDate: customerDetail.birthDate,
            
            // 证件信息
            idType: customerDetail.idType,
            idNumber: customerDetail.idNumber,
            idStartDate: customerDetail.idStartDate,
            idEndDate: customerDetail.idEndDate,
            idAddress: customerDetail.idAddress,
            contactAddress: customerDetail.contactAddress,
            
            // 联系信息
            callingCode: customerDetail.callingCode,
            mobile: customerDetail.mobile,
            email: customerDetail.email,
            
            // 工作信息
            occupation: customerDetail.occupation,
            companyName: customerDetail.companyName,
            companyAddress: customerDetail.companyAddress,
            workDetail: customerDetail.workDetail,
            
            // 身体信息
            high: customerDetail.high,
            weight: customerDetail.weight,
            izSmoking: customerDetail.izSmoking,
            izMarried: customerDetail.izMarried,
            
            // 资产信息
            annualIncome: customerDetail.annualIncome,
            deposit: customerDetail.deposit,
            stock: customerDetail.stock,
            house: customerDetail.house,
            oldThing: customerDetail.oldThing,
            otherMoney: customerDetail.otherMoney
          };
          
          // 更新表单字段值，以通过验证
          setFieldsValue({
            policyHolderInfo: customerDetail.customerName || 'filled'
          });
        }

        // 强制转换为字符串
        const customerSource = String(customerDetail.customerSource || '');
        console.log('转换后的客户区域:', customerSource, '是否等于"1":', customerSource === '1');

        const oldCustomerSource = String(formValues.customerSource || '');
        const productId = formValues.productId;

        // 设置客户区域到表单
        await setFieldsValue({
          customerSource: customerSource
        });
        console.log('设置客户区域成功:', customerSource);

        // 检查客户区域是否变化
        if (customerSource !== oldCustomerSource) {
          console.log('客户区域发生变化，从', oldCustomerSource, '变为', customerSource);

          if (customerSource === '1') {
            // 大陆客户，强制设置为年缴
            console.log('检测到大陆客户，立即设置年缴');
            updateSchema([
              {
                field: 'paymentFrequency',
                componentProps: {
                  placeholder: '大陆客户只能选择年缴',
                  options: [{ label: '年缴', value: '4' }],
                  disabled: false,
                },
              }
            ]);

            await setFieldsValue({
              paymentFrequency: '4'
            });
            console.log('已设置缴费频率为年缴(4)');
          } else if (productId) {
            // 非大陆客户且已选产品，重新加载产品支持的缴费频率
            console.log('非大陆客户且已选产品，重新加载产品相关选项');
            await updateProductRelatedOptions(productId);
          } else {
            // 非大陆客户且未选产品，清空相关选项
            console.log('非大陆客户且未选产品，清空相关选项');
            clearProductRelatedOptions();
          }
        }
      } else {
        // 清空客户信息
        customerInfo.value = null;
        // 如果不是新客户模式，也清空投保人信息
        const formValues = getFieldsValue();
        const isNewCustomer = formValues.isNewCustomer;
        if (!isNewCustomer) {
          policyHolderInfo.value = null;
          setFieldsValue({
            policyHolderInfo: ''
          });
        }
      }
    } catch (error) {
      console.error('处理客户选择事件失败:', error);
    }
  }

  // 更新产品相关的选项（缴费频率、缴费年期、缴费币种）
  async function updateProductRelatedOptions(productId: string | number) {
    try {
      console.log('开始获取产品详情，产品ID:', productId);
      if (!productId) {
        console.warn('产品ID为空，无法获取产品详情');
        clearProductRelatedOptions();
        return;
      }

      // 检查缓存中是否已有该产品详情
      if (productDetailCache.value[productId]) {
        console.log('使用缓存的产品详情:', productId);
        updateFormWithProductDetail(productDetailCache.value[productId]);
        return;
      }

      // 获取产品详情
      console.log('调用API获取产品详情');
      const productDetail = await getProductDetail(productId);
      console.log('获取到的产品详情:', productDetail);

      if (!productDetail) {
        console.warn('获取产品详情失败，返回为空');
        clearProductRelatedOptions();
        return;
      }

      // 缓存产品详情
      productDetailCache.value[productId] = productDetail;

      // 更新表单
      updateFormWithProductDetail(productDetail);
    } catch (error) {
      console.error('更新产品相关选项失败:', error);
      createMessage.error('获取产品选项失败，请重试');
      clearProductRelatedOptions();
    }
  }

  // 使用产品详情更新表单
  function updateFormWithProductDetail(productDetail: any) {
    try {
      if (!productDetail) {
        console.warn('产品详情为空，无法更新表单');
        clearProductRelatedOptions();
        return;
      }

      console.log('开始使用产品详情更新表单');
      // 获取字典数据
      const frequencyDict = getDictItemsByCode('payment_frequency') || [];
      const currencyDict = getDictItemsByCode('payment_currency') || [];

      console.log('获取到的字典数据:', { 
        frequencyDict, 
        currencyDict,
        productFrequencies: productDetail.frequencies,
        productCurrencies: productDetail.currencies,
        productTerms: productDetail.paymentTerms
      });

      // 处理缴费频率选项
      const frequencyOptions = Array.isArray(productDetail.frequencies) 
        ? productDetail.frequencies.map((freq: string) => {
            const dictItem = frequencyDict.find((item: any) => item.value === freq);
            return {
              label: dictItem ? dictItem.text : freq,
              value: freq,
            };
          }) 
        : [];

      // 处理缴费币种选项
      const currencyOptions = Array.isArray(productDetail.currencies)
        ? productDetail.currencies.map((currency: string) => {
            const dictItem = currencyDict.find((item: any) => item.value === currency);
            return {
              label: dictItem ? dictItem.text : currency,
              value: currency,
            };
          })
        : [];

      // 处理缴费年期选项
      const termOptions = Array.isArray(productDetail.paymentTerms)
        ? productDetail.paymentTerms.map((term: any) => ({
            label: `${term.paymentTerm}年`,
            value: term.paymentTerm,
          }))
        : [];

      console.log('处理后的选项:', {
        frequencyOptions,
        currencyOptions,
        termOptions,
      });

      // 获取当前客户区域
      const currentValues = getFieldsValue();
      // 强制转换为字符串
      const customerSource = String(currentValues.customerSource || '');
      console.log('updateFormWithProductDetail中的客户区域:', customerSource, '是否等于"1":', customerSource === '1');

      // 将选项保存到表单模型中，以便在表单渲染时使用
      setFieldsValue({
        paymentFrequencyOptions: frequencyOptions,
        paymentCurrencyOptions: currencyOptions,
        paymentTermOptions: termOptions
      });

      console.log('保存选项到表单模型:', {
        paymentFrequencyOptions: frequencyOptions,
        paymentCurrencyOptions: currencyOptions,
        paymentTermOptions: termOptions
      });

      // 更新表单字段配置
      try {
        updateSchema([
          {
            field: 'paymentFrequency',
            componentProps: {
              placeholder: customerSource === '1' ? '大陆客户只能选择年缴' : '请选择缴费频率',
              options: customerSource === '1' ? [{ label: '年缴', value: '4' }] : frequencyOptions,
              disabled: customerSource === '1' ? false : frequencyOptions.length === 0,
            },
          },
          {
            field: 'paymentCurrency',
            componentProps: {
              placeholder: '请选择缴费币种',
              options: currencyOptions,
              disabled: currencyOptions.length === 0,
            },
          },
          {
            field: 'paymentTerm',
            componentProps: {
              placeholder: '请选择缴费年期',
              options: termOptions,
              disabled: termOptions.length === 0,
            },
          },
        ]);
        console.log('表单字段配置更新完成');
      } catch (schemaError) {
        console.error('更新表单字段配置失败:', schemaError);
      }

      // 清空之前选择的值（如果新产品不支持）
      const updates: any = {};

      // 如果是大陆客户，强制设置为年缴
      if (customerSource === '1') {
        updates.paymentFrequency = '4';
      } 
      // 否则检查当前选择是否在新选项中
      else if (currentValues.paymentFrequency && !frequencyOptions.some(opt => opt.value === currentValues.paymentFrequency)) {
        updates.paymentFrequency = undefined;
      }

      if (currentValues.paymentCurrency && !currencyOptions.some(opt => opt.value === currentValues.paymentCurrency)) {
        updates.paymentCurrency = undefined;
      }
      if (currentValues.paymentTerm && !termOptions.some(opt => opt.value === currentValues.paymentTerm)) {
        updates.paymentTerm = undefined;
      }

      if (Object.keys(updates).length > 0) {
        console.log('更新表单值:', updates);
        setFieldsValue(updates);
      }
    } catch (error) {
      console.error('更新表单选项失败:', error);
      clearProductRelatedOptions();
    }
  }

  // 清空产品相关选项
  function clearProductRelatedOptions() {
    // 获取当前客户区域
    const currentValues = getFieldsValue();
    // 强制转换为字符串
    const customerSource = String(currentValues.customerSource || '');
    console.log('clearProductRelatedOptions中的客户区域:', customerSource, '是否等于"1":', customerSource === '1');

    // 根据客户区域设置不同的选项
    const frequencyOptions = customerSource === '1' ? [{ label: '年缴', value: '4' }] : [];
    const frequencyDisabled = customerSource === '1' ? false : true;
    const frequencyPlaceholder = customerSource === '1' ? '大陆客户只能选择年缴' : '请先选择产品';
    console.log('设置的选项:', { frequencyOptions, frequencyDisabled, frequencyPlaceholder });

    updateSchema([
      {
        field: 'paymentFrequency',
        componentProps: {
          placeholder: frequencyPlaceholder,
          options: frequencyOptions,
          disabled: frequencyDisabled,
        },
      },
      {
        field: 'paymentCurrency',
        componentProps: {
          placeholder: '请先选择产品',
          options: [],
          disabled: true,
        },
      },
      {
        field: 'paymentTerm',
        componentProps: {
          placeholder: '请先选择产品',
          options: [],
          disabled: true,
        },
      },
    ]);

    // 清空已选值和选项列表
    const updates: any = {
      paymentFrequencyOptions: [],
      paymentCurrencyOptions: [],
      paymentTermOptions: [],
      paymentCurrency: undefined,
      paymentTerm: undefined
    };

    // 如果是大陆客户，设置为年缴，否则清空
    if (customerSource === '1') {
      updates.paymentFrequency = '4';
    } else {
      updates.paymentFrequency = undefined;
    }

    setFieldsValue(updates);
  }

  // 添加附加险
  function handleAddAdditionalInsurance() {
    const formValues = getFieldsValue();
    const supplierNo = formValues.supplierNo;
    const productType = formValues.productType;

    if (!supplierNo) {
      createMessage.warning('请先选择供应商');
      return;
    }

    if (!productType) {
      createMessage.warning('请先选择产品类型');
      return;
    }

    openAdditionalModal(true, {
      register: registerAdditionalModal,
      isUpdate: false,
      supplierNo,
      productType,
    });
  }

  // 编辑附加险
  function handleEditAdditionalInsurance(record: Recordable) {
    const formValues = getFieldsValue();
    const supplierNo = formValues.supplierNo;
    const productType = formValues.productType;

    if (!supplierNo) {
      createMessage.warning('请先选择供应商');
      return;
    }

    if (!productType) {
      createMessage.warning('请先选择产品类型');
      return;
    }

    openAdditionalModal(true, {
      register: registerAdditionalModal,
      isUpdate: true,
      record,
      supplierNo,
      productType,
    });
  }

  // 删除附加险
  function handleRemoveAdditionalInsurance(record: Recordable) {
    const index = additionalInsuranceList.value.findIndex(item => item.id === record.id);
    if (index !== -1) {
      additionalInsuranceList.value.splice(index, 1);
    }
  }

  // 处理附加险表单提交
  function handleAdditionalInsuranceSuccess(formData: Recordable) {
    if (!formData.id) {
      // 新增 - 使用UUID作为临时ID
      //formData.id = uuidv4();
      additionalInsuranceList.value.push(formData);
    } else {
      // 编辑
      const index = additionalInsuranceList.value.findIndex(item => item.id === formData.id);
      if (index !== -1) {
        additionalInsuranceList.value[index] = formData;
      }
    }
  }

  // 编辑投保人信息
  function handleEditPolicyHolder() {
    openPersonInfoModal(true, {
      register: registerPersonInfoModal,
      isUpdate: !!policyHolderInfo.value,
      record: policyHolderInfo.value || {},
      personType: 'policyHolder',
      customerInfo: customerInfo.value,
      policyHolderInfo: policyHolderInfo.value,
    });
  }

  // 编辑被保人信息
  function handleEditInsured() {
    openPersonInfoModal(true, {
      register: registerPersonInfoModal,
      isUpdate: !!insuredInfo.value,
      record: insuredInfo.value || {},
      personType: 'insured',
      customerInfo: customerInfo.value,
      policyHolderInfo: policyHolderInfo.value,
    });
  }

  // 添加受益人信息
  function handleAddBeneficiary() {
    openPersonInfoModal(true, {
      register: registerPersonInfoModal,
      isUpdate: false,
      record: {},
      personType: 'beneficiary',
      customerInfo: customerInfo.value,
      policyHolderInfo: policyHolderInfo.value,
    });
  }

  // 编辑受益人信息
  function handleEditBeneficiary(record: Recordable) {
    openPersonInfoModal(true, {
      register: registerPersonInfoModal,
      isUpdate: true,
      record: record || {},
      personType: 'beneficiary',
      customerInfo: customerInfo.value,
      policyHolderInfo: policyHolderInfo.value,
    });
  }

  // 删除受益人信息
  function handleRemoveBeneficiary(record: Recordable) {
    const index = beneficiaryList.value.findIndex(item => item.id === record.id);
    if (index !== -1) {
      beneficiaryList.value.splice(index, 1);
      // 检查受益比例总和
      validateBeneficiaryRatio();
    }
  }

  // 验证受益人比例总和
  function validateBeneficiaryRatio() {
    const total = unref(totalBeneficiaryRatio);
    // 如果有受益人，则总比例必须为100%
    beneficiaryRatioError.value = beneficiaryList.value.length > 0 && Math.abs(total - 100) > 0.01;
    return !beneficiaryRatioError.value;
  }

  // 处理个人信息表单提交
  function handlePersonInfoSuccess(result: { type: string; data: Recordable }) {
    try {
      console.log('收到个人信息表单提交结果:', result);
      const { type, data } = result;

      switch (type) {
        case 'policyHolder':
          policyHolderInfo.value = data;
          // 更新表单字段值，以通过验证
          setFieldsValue({
            policyHolderInfo: data.id || data.name || 'filled' // 确保有有效值通过验证
          });
          break;
        case 'insured':
          insuredInfo.value = data;
          // 更新表单字段值，以通过验证
          setFieldsValue({
            insuredInfo: data.id || data.name || 'filled' // 确保有有效值通过验证
          });
          break;
        case 'beneficiary':
          // 处理受益人信息 - 新增或更新列表中的项
          if (data.id) {
            // 编辑现有受益人
            const index = beneficiaryList.value.findIndex(item => item.id === data.id);
            if (index !== -1) {
              beneficiaryList.value[index] = data;
            } else {
              // 如果找不到对应ID，则添加为新项
              beneficiaryList.value.push(data);
            }
          } else {
            // 新增受益人，生成临时ID
            //data.id = `${Date.now()}`;
            beneficiaryList.value.push(data);
          }

          // 验证受益比例总和
          validateBeneficiaryRatio();

          // 更新表单字段值，以通过验证
          setFieldsValue({
            beneficiaryInfo: beneficiaryList.value.length > 0 ? 'filled' : '' // 确保有有效值通过验证
          });
          break;
      }
    } catch (error) {
      console.error('处理个人信息表单提交失败:', error);
    }
  }

  // 更新表单字段的动态配置
  function updateFormFieldsConfig() {
    try {
      console.log('开始更新表单字段配置');
      const currentUserInfo = userInfo as any;
      const currentActionType = unref(actionType);

      console.log('当前用户信息:', currentUserInfo);
      console.log('当前操作类型:', currentActionType);

      // 更新核保是否通过字段
      try {
        updateSchema({
          field: 'auditResult',
          ifShow: currentUserInfo?.postCode === 'operationalPost' && currentActionType === 'underwritingReply',
          required: currentUserInfo?.postCode === 'operationalPost' && currentActionType === 'underwritingReply',
        });
      } catch (error) {
        console.error('更新核保是否通过字段失败:', error);
      }

      // 更新照会回复字段
      try {
        updateSchema({
          field: 'noticeReply',
          ifShow: currentUserInfo?.postCode === 'customerManager' && currentActionType === 'noticeReply',
          required: currentUserInfo?.postCode === 'customerManager' && currentActionType === 'noticeReply',
        });
      } catch (error) {
        console.error('更新照会回复字段失败:', error);
      }

      // 更新变更原因字段 - 需要在表单值变化时动态判断
      try {
        updateSchema({
          field: 'changeReason',
          ifShow: ({ values }) => {
            return currentUserInfo?.postCode === 'operationalPost' && currentActionType === 'policyEdit' && values.policyStatus === '9';
          },
          required: ({ values }) => {
            return currentUserInfo?.postCode === 'operationalPost' && currentActionType === 'policyEdit' && values.policyStatus === '9';
          },
        });
      } catch (error) {
        console.error('更新变更原因字段失败:', error);
      }

      console.log('表单字段配置更新完成');
    } catch (error) {
      console.error('更新表单字段配置失败:', error);
    }
  }

  // 文件上传相关
  const fileList = ref<any[]>([]);
  const fileUrls = ref<string[]>([]);
  const fileTypeVisible = ref(false);
  const currentFileType = ref('');
  const currentFileUrl = ref('');
  const fileTypeOptions = ref<any[]>([]);
  const uploadLoading = ref(false);
  
  // 文件表格列定义
  const fileColumns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '文件类型',
      dataIndex: 'fileTypeCode',
      key: 'fileType',
      width: 130,
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      customRender: ({ text, record }) => {
        // 优先使用response中的fileSize，如果没有则使用size
        const fileSize = record.response?.fileSize || record.size || 0;
        return formatFileSize(fileSize);
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 135,
      align: 'center',
    },
  ];
  
  // 格式化文件大小
  function formatFileSize(size) {
    if (!size) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let index = 0;
    let fileSize = size;
    
    while (fileSize >= 1024 && index < units.length - 1) {
      fileSize /= 1024;
      index++;
    }
    
    return `${fileSize.toFixed(2)} ${units[index]}`;
  }
  
  // 获取文件类型字典
  onMounted(() => {
    loadFileTypeOptions();
  });
  
  // 加载文件类型选项
  function loadFileTypeOptions() {
    try {
      console.log('开始加载文件类型字典...');
      // 先尝试从缓存获取
      const dictItems = getDictItemsByCode('policy_file_type');
      console.log('获取到的文件类型字典:', dictItems);
      
      if (dictItems && Array.isArray(dictItems) && dictItems.length > 0) {
        fileTypeOptions.value = dictItems;
        console.log('成功从缓存加载文件类型字典');
      } else {
        console.warn('从缓存中未找到文件类型字典，尝试从后端获取');
        // 缓存中没有，直接从后端获取
        initDictOptions('policy_file_type')
          .then(res => {
            console.log('从后端获取的文件类型字典:', res);
            if (res && Array.isArray(res) && res.length > 0) {
              fileTypeOptions.value = res;
              console.log('成功从后端加载文件类型字典');
            } else {
              console.warn('从后端获取文件类型字典失败，使用默认选项');
              // 使用默认选项
              setDefaultFileTypeOptions();
            }
          })
          .catch(err => {
            console.error('从后端获取文件类型字典出错:', err);
            // 出错时使用默认选项
            setDefaultFileTypeOptions();
          });
      }
    } catch (error) {
      console.error('加载文件类型字典失败:', error);
      // 出错时使用默认选项
      setDefaultFileTypeOptions();
    } finally {
      // 确保无论如何都有默认选项
      if (!fileTypeOptions.value || !Array.isArray(fileTypeOptions.value) || fileTypeOptions.value.length === 0) {
        console.warn('文件类型选项为空，设置默认选项');
        setDefaultFileTypeOptions();
      }
      console.log('最终的文件类型选项:', fileTypeOptions.value);
    }
  }
  
  // 设置默认的文件类型选项
  function setDefaultFileTypeOptions() {
    fileTypeOptions.value = [
      { value: '1', text: '保单文件' },
      { value: '2', text: '核保文件' },
      { value: '3', text: '其他文件' }
    ];
    console.log('已设置默认文件类型选项');
  }
  
  // 上传前检查
  function beforeUpload(file) {
    // 检查文件大小，限制为20MB
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      createMessage.error('文件必须小于20MB!');
      return false;
    }
    return true;
  }
  
  // 处理文件上传变更
  async function handleUploadChange(info) {
    const { file, fileList: uploadFileList } = info;
    
    // 当文件状态为uploading时
    if (file.status === 'uploading') {
      uploadLoading.value = true;
      return;
    }
    
    // 当文件上传完成时
    if (file.status === 'done') {
      uploadLoading.value = false;
      
      // 检查上传是否成功
      if (file.response && (file.response.success || file.response.code === 200)) {
        // 获取文件URL，根据API响应格式调整
        const fileUrl = file.response.message || file.response.result || '';
        
        console.log('文件上传响应:', file.response);
        
        // 从响应中提取文件大小信息
        const fileSize = file.response.fileSize || file.size || 0;
        
        // 创建文件对象
        const fileObj = {
          uid: file.uid || Date.now() + Math.random().toString(36).substring(2, 10),
          name: file.name,
          status: 'done',
          url: fileUrl,
          fileUrl: fileUrl,
          fileTypeCode: '', // 初始为空，需要用户选择
          fileTypeName: '',
          size: fileSize, // 使用从响应中获取的文件大小
          type: file.type,
          response: {
            fileUrl: fileUrl,
            fileName: file.name,
            fileType: file.type,
            fileSize: fileSize, // 同时保存在response对象中
            uploadTime: new Date().toISOString(),
          }
        };
        
        // 添加到文件列表
        fileList.value.push(fileObj);
        
        // 更新URL列表
        if (!fileUrls.value.includes(fileUrl)) {
          fileUrls.value.push(fileUrl);
        }
        
        console.log('文件上传成功:', fileObj);
        console.log('当前文件列表:', fileList.value);
        
        // 提示用户选择文件类型
        createMessage.success(`${file.name} 上传成功，请选择文件类型`);
      } else {
        // 上传失败
        createMessage.error(`${file.name} 上传失败: ${file.response?.message || '未知错误'}`);
      }
    } else if (file.status === 'error') {
      uploadLoading.value = false;
      createMessage.error(`${file.name} 上传失败: ${file.response?.message || '网络错误'}`);
      console.error('文件上传错误:', file.error, file.response);
    }
  }
  
  // 处理文件类型变更
  function handleFileTypeChange(record, value) {
    // 更新文件类型
    record.fileTypeCode = value;
    
    // 查找文件类型文本
    const fileTypeItem = fileTypeOptions.value.find(item => item.value === value);
    record.fileTypeName = fileTypeItem?.text || '';
    
    console.log('文件类型已更新:', record);
  }
  
  // 下载文件
  function handleDownloadFile(record) {
    try {
      // 获取文件URL，优先使用response中的url，其次使用record.url
      const fileUrl = record.response?.url || record.url;
      
      if (!fileUrl) {
        createMessage.warning('文件下载链接不存在');
        return;
      }
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = record.name || '下载文件';
      link.target = '_blank';
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log('文件下载:', record);
    } catch (error) {
      console.error('文件下载失败:', error);
      createMessage.error('文件下载失败');
    }
  }

  // 删除文件
  function handleRemoveFile(record) {
    // 从文件列表中移除
    const index = fileList.value.findIndex(file => file.uid === record.uid);
    if (index !== -1) {
      fileList.value.splice(index, 1);
    }
    
    // 从URL列表中移除
    const urlIndex = fileUrls.value.findIndex(url => url === record.url);
    if (urlIndex !== -1) {
      fileUrls.value.splice(urlIndex, 1);
    }
    
    console.log('文件已删除:', record);
    console.log('当前文件列表:', fileList.value);
    console.log('当前URL列表:', fileUrls.value);
  }

  // 注册弹窗
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    try {
      console.log('弹窗初始化开始，接收到的数据:', data);
      // 重置弹窗初始化标志
      modalInitialized.value = false;
      console.log('重置弹窗初始化标志为 false');
      resetFields();
      clearProductRelatedOptions();
      setModalProps({ confirmLoading: false });
      isUpdate.value = !!data?.isUpdate;
      actionType.value = data?.actionType || '';
      console.log('设置isUpdate:', isUpdate.value, '设置actionType:', actionType.value);

      // 更新表单字段的动态配置
      updateFormFieldsConfig();

      // 清空附加险列表和个人信息
      additionalInsuranceList.value = [];
      policyHolderInfo.value = null;
      insuredInfo.value = null;
      beneficiaryList.value = [];
      beneficiaryRatioError.value = false;
      
      // 清空文件列表
      fileList.value = [];
      fileUrls.value = [];
      
      // 重新加载文件类型选项
      loadFileTypeOptions();

      if (unref(isUpdate)) {
        try {
          // 保存原始记录，以便后续使用ID等信息
          record.value = { ...data.record };
          console.log('编辑模式，原始记录:', record.value);

          // 编辑模式下，调用API获取完整的订单详情
          console.log('正在获取订单详情，ID:', data.record.id);
          setModalProps({ confirmLoading: true });

          try {
            // 调用API获取详细数据
            console.log('开始调用getPolicyDetail API');
            const detailData = await getPolicyDetail(data.record.id);
            console.log('获取到的订单详情数据:', detailData);

            // 记录产品ID，以便后续处理
            const productId = detailData.productId;
            console.log('订单详情中的产品ID:', productId);

            // 使用获取到的详情数据填充表单
            console.log('开始设置表单值');
            await setFieldsValue({
              ...detailData,
            });
            
            // 等待一个tick确保表单数据已经设置完成
            await nextTick();
            console.log('表单数据设置完成后的值:', getFieldsValue());

            // 如果有客户ID，手动触发客户变化处理逻辑
            if (detailData.customerId) {
              console.log('编辑模式：手动触发客户变化处理，客户ID:', detailData.customerId);
              await handleCustomerChange(detailData.customerId);
            }

            // 确保产品ID被正确设置
            if (productId) {
              console.log('设置产品ID:', productId);
              // 直接设置产品ID
              await setFieldsValue({
                productId: productId
              });
              
              // 再次等待确保数据设置完成
              await nextTick();
              console.log('产品ID设置完成，当前表单值:', getFieldsValue());
              
              // 加载产品相关选项（这会根据当前的客户区域来设置选项）
              await updateProductRelatedOptions(productId);
            }

            // 处理附加险数据
            if (detailData.crmPolicyAdditionals && Array.isArray(detailData.crmPolicyAdditionals)) {
              console.log('设置附加险数据:', detailData.crmPolicyAdditionals);
              additionalInsuranceList.value = [...detailData.crmPolicyAdditionals];
            }

            // 处理投保人信息
            if (detailData.crmPolicyHolders) {
              console.log('设置投保人信息:', detailData.crmPolicyHolders);
              policyHolderInfo.value = detailData.crmPolicyHolders;
            }

            // 处理被保人信息
            if (detailData.crmPolicyInsureds) {
              console.log('设置被保人信息:', detailData.crmPolicyInsureds);
              insuredInfo.value = detailData.crmPolicyInsureds;
            }

            // 处理受益人信息
            if (detailData.crmPolicyBeneficiaries) {
              console.log('处理受益人信息');
              if (Array.isArray(detailData.crmPolicyBeneficiaries)) {
                beneficiaryList.value = [...detailData.crmPolicyBeneficiaries];
              } else {
                // 兼容旧数据，将单个对象转为数组
                beneficiaryList.value = [detailData.crmPolicyBeneficiaries];
              }
              console.log('设置受益人列表:', beneficiaryList.value);
              // 验证受益比例总和
              validateBeneficiaryRatio();
            }
            
            // 处理文件数据
            if (detailData.crmPolicyFiles && Array.isArray(detailData.crmPolicyFiles)) {
              console.log('处理文件数据:', detailData.crmPolicyFiles);
              try {
                // 设置文件列表
                fileList.value = detailData.crmPolicyFiles.map((file: any) => {
                  // 确保文件大小正确保存
                  const fileSize = file.fileSize || 0;
                  console.log(`文件 ${file.fileName} 的大小: ${fileSize}`);
                  
                  return {
                    uid: file.id || Date.now() + Math.random().toString(36).substring(2, 10),
                    name: file.fileName || '',
                    status: 'done',
                    url: file.filePath || '',
                    fileUrl: file.filePath || '',
                    fileTypeCode: file.fileType || '',
                    fileTypeName: getDictText('policy_file_type', file.fileType) || '',
                    size: fileSize, // 直接使用文件大小
                    type: file.fileContentType || '',
                    response: {
                      fileUrl: file.filePath || '',
                      fileName: file.fileName || '',
                      fileType: file.fileContentType || '',
                      fileSize: fileSize, // 同时保存在response对象中
                      uploadTime: file.createTime || new Date().toISOString(),
                    }
                  };
                });
                
                // 同时设置文件URL列表
                fileUrls.value = fileList.value.map(file => file.url);
                console.log('设置文件列表完成:', fileList.value);
              } catch (fileError) {
                console.error('处理文件数据时出错:', fileError);
              }
            }

          } catch (detailError) {
            console.error('获取订单详情失败:', detailError);
            createMessage.error('获取订单详情失败，请重试');

            // 如果获取详情失败，回退到使用传入的数据
            console.log('回退到使用传入的数据');
            await setFieldsValue({
              ...data.record,
            });

            // 如果有附加险数据，加载到列表中
            if (data.record.crmPolicyAdditionals && Array.isArray(data.record.crmPolicyAdditionals)) {
              additionalInsuranceList.value = [...data.record.crmPolicyAdditionals];
            }

            // 如果有个人信息数据，加载到对应字段
            if (data.record.crmPolicyHolders) {
              policyHolderInfo.value = data.record.crmPolicyHolders;
            }

            if (data.record.crmPolicyInsureds) {
              insuredInfo.value = data.record.crmPolicyInsureds;
            }

            if (data.record.crmPolicyBeneficiaries) {
              if (Array.isArray(data.record.crmPolicyBeneficiaries)) {
                beneficiaryList.value = [...data.record.crmPolicyBeneficiaries];
              } else {
                // 兼容旧数据，将单个对象转为数组
                beneficiaryList.value = [data.record.crmPolicyBeneficiaries];
              }
              // 验证受益比例总和
              validateBeneficiaryRatio();
            }
            
            // 处理文件数据
            if (data.record.crmPolicyFiles && Array.isArray(data.record.crmPolicyFiles)) {
              try {
                fileList.value = data.record.crmPolicyFiles.map((file: any) => {
                  // 确保文件大小正确保存
                  const fileSize = file.fileSize || 0;
                  console.log(`文件 ${file.fileName} 的大小: ${fileSize}`);
                  
                  return {
                    uid: file.id || Date.now() + Math.random().toString(36).substring(2, 10),
                    name: file.fileName || '',
                    status: 'done',
                    url: file.filePath || '',
                    fileUrl: file.filePath || '',
                    fileTypeCode: file.fileType || '',
                    fileTypeName: getDictText('policy_file_type', file.fileType) || '',
                    size: fileSize, // 直接使用文件大小
                    type: file.fileContentType || '',
                    response: {
                      fileUrl: file.filePath || '',
                      fileName: file.fileName || '',
                      fileType: file.fileContentType || '',
                      fileSize: fileSize, // 同时保存在response对象中
                      uploadTime: file.createTime || new Date().toISOString(),
                    }
                  };
                });
                
                // 同时设置文件URL列表
                fileUrls.value = fileList.value.map(file => file.url);
                console.log('设置文件列表完成:', fileList.value);
              } catch (fileError) {
                console.error('处理文件数据时出错:', fileError);
              }
            }
          } finally {
            setModalProps({ confirmLoading: false });
          }
        } catch (updateError) {
          console.error('编辑模式处理失败:', updateError);
          setModalProps({ confirmLoading: false });
        }
      } else {
        console.log('新增模式初始化完成');
      }
      console.log('弹窗初始化完成');
      // 设置弹窗初始化完成标志
      modalInitialized.value = true;
      console.log('设置弹窗初始化完成标志为 true');
    } catch (error) {
      console.error('初始化表单失败:', error);
      setModalProps({ confirmLoading: false });
    }
  });
  


  // 保存草稿
  async function handleSaveDraft() {
    try {
      await submitForm('0', false); // 传入false表示不进行表单验证
    } catch (error: any) {
      console.error('保存预约失败:', error);
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      await submitForm('1', true); // 传入true表示需要进行表单验证
    } catch (error: any) {
      console.error('提交表单失败:', error);
    }
  }

  // 通用表单提交处理
  async function submitForm(izSubmit: string, needValidate: boolean = true) {
    try {
      // 验证受益人比例总和
      if (beneficiaryList.value.length > 0 && !validateBeneficiaryRatio()) {
        createMessage.error(`受益人受益比例总和必须为100%，当前总和: ${totalBeneficiaryRatio.value}%`);
        return;
      }
      
      // 确保投保人信息等字段值正确设置
      if (policyHolderInfo.value) {
        setFieldsValue({
          policyHolderInfo: policyHolderInfo.value.id || policyHolderInfo.value.name || 'filled'
        });
      }
      if (insuredInfo.value) {
        setFieldsValue({
          insuredInfo: insuredInfo.value.id || insuredInfo.value.name || 'filled'
        });
      }
      if (beneficiaryList.value.length > 0) {
        setFieldsValue({
          beneficiaryInfo: 'filled'
        });
      }
      
      // 检查文件列表
      if (fileList.value.length > 0) {
        // 检查是否有未设置类型的文件
        const unsetTypeFile = fileList.value.find(file => !file.fileTypeCode);
        if (unsetTypeFile) {
          createMessage.error('有文件未设置文件类型，请设置后再提交');
          return;
        }
      }
      
      // 根据needValidate参数决定是否进行表单验证
      const values = needValidate ? await validate() : getFieldsValue();
      confirmLoading.value = true;
      setModalProps({ confirmLoading: true });

      // 处理表单数据
      const formData = {
        ...values,
        izSubmit, // 设置是否提交标志
        crmPolicyAdditionals: additionalInsuranceList.value,
        crmPolicyHolders: policyHolderInfo.value,
        crmPolicyInsureds: insuredInfo.value,
        crmPolicyBeneficiaries: beneficiaryList.value,
      };
      
      // 添加文件列表（如果有）
      if (fileList.value.length > 0) {
        formData.crmPolicyFiles = fileList.value.map(file => {
          // 确保文件大小正确保存
          const fileSize = file.response?.fileSize || file.size || 0;
          
          return {
            fileName: file.name,
            filePath: file.url || file.fileUrl,
            fileType: file.fileTypeCode,
            fileContentType: file.type || file.response?.fileType,
            fileSize: fileSize
          };
        });
        console.log('提交的文件列表:', formData.crmPolicyFiles);
      } else {
        formData.crmPolicyFiles = [];
      }

      // 如果是更新操作，需要传入ID
      if (unref(isUpdate)) {
        formData.id = record.value.id;
      }

      console.log('提交的表单数据:', formData);

      // 根据操作类型调用不同的API
      //运营岗审核提交
      if (unref(actionType) === 'operationAudit') {
        await operationAudit(formData);
      } else if (unref(actionType) === 'underwritingReply') {
        await underwritingReply(formData);
      } else if(unref(actionType) === 'feesArrival') {
        await feesArrival(formData);
      } else if(unref(actionType) === 'policyIssuance') {
        await policyIssuance(formData);
      } else if(unref(actionType) === 'policyComplete') {
        await policyComplete(formData);
      }else if(unref(actionType) === 'noticeReply'){
        await noticeReply(formData);
      }else if(unref(actionType) === 'policyEdit'){
        await policyEdit(formData);
      }else {
        // 销售员保存/提交
        await saveOrUpdate(formData, unref(isUpdate));
      }

      handleCloseModal();
      emit('success');
      //createMessage.success(izSubmit === '1' ? `提交成功` : `保存草稿成功`);
    } catch (error: any) {
      console.error('操作失败:', error);
     // createMessage.error(`操作失败: ${error?.message || '请填写必填字段'}`);
    } finally {
      confirmLoading.value = false;
      setModalProps({ confirmLoading: false });
    }
  }

  // 发送签字邮件
  async function handleSendSignatureEmail() {
    try {
      const formData = unref(currentFormData);
      const currentRecord = unref(record);

      if (!currentRecord || !currentRecord.id) {
        createMessage.error('保单信息不存在');
        return;
      }

      // 获取客户邮箱
      const email = formData?.notificationEmail;
      if (!email) {
        createMessage.error('客户邮箱地址为空，请先设置客户邮箱');
        return;
      }

      emailLoading.value = true;

      // 发送邮件
      await sendSignatureEmail({
        policyId: currentRecord.id,
        email: email,
        customerName: formData.customerName || ''
      });

      createMessage.success('签字邮件发送成功！');
    } catch (error: any) {
      console.error('发送邮件失败:', error);
      createMessage.error(`发送邮件失败: ${error?.message || '未知错误'}`);
    } finally {
      emailLoading.value = false;
    }
  }

  // 复制签字链接
  function copySignatureLink() {
    const formData = unref(currentFormData);
    if (formData?.signatureSms) {
      navigator.clipboard.writeText(formData.signatureSms).then(() => {
        createMessage.success('签字链接已复制到剪贴板');
      }).catch(() => {
        createMessage.error('复制失败，请手动复制');
      });
    } else {
      createMessage.error('签字链接不存在');
    }
  }

  // 打开签字链接
  function openSignatureLink() {
    const formData = unref(currentFormData);
    if (formData?.signatureSms) {
      window.open(formData.signatureSms, '_blank');
    } else {
      createMessage.error('签字链接不存在');
    }
  }
</script>

<style lang="less" scoped>
.policy-files {
  margin-bottom: 16px;
}
</style>
