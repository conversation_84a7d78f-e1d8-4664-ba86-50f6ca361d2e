# 关系自动复制功能实现说明

## 功能描述

实现了在保单管理系统中，当选择人员关系为"本人"时自动复制相关信息的功能：

1. **被保人信息**：当选择与投保人关系为"本人"(值=1)时，自动复制投保人信息到被保人信息
2. **受益人信息**：当选择与被保人关系为"本人"(值=1)时，自动复制被保人信息到受益人信息

## 修改的文件

### 1. PersonInfoModal.vue

#### 新增功能：
- 添加了 `insuredInfo` 引用来存储被保人信息
- 修改了 `copySourceInfo` 函数，使受益人复制被保人信息而不是投保人信息
- 新增了 `watch` 监听器来监听关系字段(`withRelation`)的变化
- 新增了 `copyInfoFromSource` 通用函数来处理信息复制

#### 核心逻辑：
```javascript
// 监听关系字段变化
watch(
  () => getFieldsValue()?.withRelation,
  async (newValue) => {
    if (newValue === '1') { // 选择"本人"
      const type = unref(personType);
      
      if (type === 'insured') {
        // 被保人选择"本人"，复制投保人信息
        await copyInfoFromSource(policyHolderInfo.value);
      } else if (type === 'beneficiary') {
        // 受益人选择"本人"，复制被保人信息
        await copyInfoFromSource(insuredInfo.value);
      }
    }
  }
);
```

### 2. CrmPoliciesModal.vue

#### 修改内容：
- 在所有打开PersonInfoModal的函数中添加了 `insuredInfo` 参数传递
- 确保被保人信息能够正确传递给PersonInfoModal组件

#### 修改的函数：
- `handleEditPolicyHolder()` - 编辑投保人信息
- `handleEditInsured()` - 编辑被保人信息  
- `handleAddBeneficiary()` - 添加受益人信息
- `handleEditBeneficiary()` - 编辑受益人信息

## 功能特点

1. **自动触发**：当用户在关系下拉框中选择"本人"时，系统自动复制相应信息
2. **智能复制**：
   - 被保人选择"本人" → 复制投保人信息
   - 受益人选择"本人" → 复制被保人信息
3. **完整复制**：复制所有相关字段，包括姓名、证件信息、联系方式、工作信息等
4. **拼音生成**：如果复制了中文姓名，会自动生成拼音和英文姓名
5. **用户提示**：复制成功后会显示提示消息
6. **错误处理**：如果没有可复制的信息，会显示警告消息

## 技术实现

- 使用Vue 3的 `watch` API监听表单字段变化
- 使用 `unref` 获取响应式引用的值
- 异步处理信息复制和拼音生成
- 错误处理确保系统稳定性

## 测试建议

1. 测试被保人信息编辑时选择"本人"关系
2. 测试受益人信息编辑时选择"本人"关系
3. 测试在没有投保人/被保人信息时的错误处理
4. 测试复制后的拼音生成功能
5. 测试关系选择为其他值时不触发复制功能
