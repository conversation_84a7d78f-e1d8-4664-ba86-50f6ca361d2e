<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="客户详情" width="1000px" :showOkBtn="false">
    <div class="customer-detail-container">
      <!-- 基本信息区域 -->
      <div class="detail-section">
        <div class="section-header">
          <a-icon type="user" class="section-icon" />
          <h3 class="section-title">基本信息</h3>
        </div>
        <div class="section-content">
          <Descriptions :column="2" bordered size="middle">
            <DescriptionsItem label="客户编号">{{ record.customerNo }}</DescriptionsItem>
            <DescriptionsItem label="客户姓名">{{ record.customerName }}</DescriptionsItem>
            <DescriptionsItem label="性别">
              {{ record.gender === 1 ? '男' : record.gender === 2 ? '女' : '未知' }}
            </DescriptionsItem>
            <DescriptionsItem label="手机号码">{{ record.mobile }}</DescriptionsItem>
            <DescriptionsItem label="证件类型">
              {{ record.idType === '1' ? '身份证' : record.idType === '2' ? '护照' : '其他' }}
            </DescriptionsItem>
            <DescriptionsItem label="证件号">{{ record.idNumber }}</DescriptionsItem>
            <DescriptionsItem label="客户等级">{{ record.cusLevel_dictText || record.cusLevelName || record.cusLevel }}</DescriptionsItem>
            <DescriptionsItem label="生日">{{ record.birthDate }}</DescriptionsItem>
            <DescriptionsItem label="电子邮箱">{{ record.email }}</DescriptionsItem>
            <DescriptionsItem label="职业信息" :span="2">{{ record.occupation }}</DescriptionsItem>
            <DescriptionsItem label="工作岗位">{{ record.jobPosition }}</DescriptionsItem>
            <DescriptionsItem label="工作单位">{{ record.workUnit }}</DescriptionsItem>
            <DescriptionsItem label="年薪">{{ record.annualSalary ? `${record.annualSalary}万元` : '' }}</DescriptionsItem>
            <DescriptionsItem label="联系地址" :span="2">{{ record.address }}</DescriptionsItem>
            <DescriptionsItem label="客户状态">
              <a-tag :color="record.customerStatus === 1 ? 'green' : 'orange'">
                {{ record.customerStatus === 1 ? '正常' : record.customerStatus === 2 ? '暂停' : '未知' }}
              </a-tag>
            </DescriptionsItem>
            <DescriptionsItem label="客户区域">
              {{ record.customerArea_dictText || record.customerArea }}
            </DescriptionsItem>
          </Descriptions>
        </div>
      </div>
      
      <!-- 资产情况区域 -->
      <div class="detail-section">
        <div class="section-header">
          <a-icon type="bank" class="section-icon" />
          <h3 class="section-title">资产情况</h3>
          <div class="total-assets">
            总资产：<span class="total-amount">{{ calculateTotalAssets() }}万元</span>
          </div>
        </div>
        <div class="section-content">
          <div class="asset-grid">
            <div class="asset-item">
              <div class="asset-label">银行存款</div>
              <div class="asset-value">{{ record.bankDeposit ? `${record.bankDeposit}万元` : '0万元' }}</div>
            </div>
            <div class="asset-item">
              <div class="asset-label">股市投资</div>
              <div class="asset-value">{{ record.stockMarket ? `${record.stockMarket}万元` : '0万元' }}</div>
            </div>
            <div class="asset-item">
              <div class="asset-label">房产价值</div>
              <div class="asset-value">{{ record.realEstate ? `${record.realEstate}万元` : '0万元' }}</div>
            </div>
            <div class="asset-item">
              <div class="asset-label">古玩字画</div>
              <div class="asset-value">{{ record.antiques ? `${record.antiques}万元` : '0万元' }}</div>
            </div>
            <div class="asset-item">
              <div class="asset-label">债券投资</div>
              <div class="asset-value">{{ record.bonds ? `${record.bonds}万元` : '0万元' }}</div>
            </div>
            <div class="asset-item">
              <div class="asset-label">其他资产</div>
              <div class="asset-value">{{ record.otherAssets ? `${record.otherAssets}万元` : '0万元' }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 客户管理信息区域 -->
      <div class="detail-section">
        <div class="section-header">
          <a-icon type="setting" class="section-icon" />
          <h3 class="section-title">客户管理信息</h3>
        </div>
        <div class="section-content">
          <Descriptions :column="2" bordered size="middle">
            <DescriptionsItem label="转介人">{{ record.referrerName || record.realname }}</DescriptionsItem>
            <DescriptionsItem label="客户来源">
              {{ record.customerSource_dictText || record.customerSource }}
            </DescriptionsItem>
            <DescriptionsItem label="客户经理">{{ record.customerManagerName }}</DescriptionsItem>
            <DescriptionsItem label="开发人员">{{ record.developerName }}</DescriptionsItem>
            <DescriptionsItem label="创建时间">{{ record.createTime }}</DescriptionsItem>
            <DescriptionsItem label="更新时间">{{ record.updateTime }}</DescriptionsItem>
            <DescriptionsItem label="备注信息" :span="2">{{ record.remark }}</DescriptionsItem>
          </Descriptions>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { Descriptions } from 'ant-design-vue';
  import { queryById } from '../CrmCustomer.api';

  // 组件注册
  const DescriptionsItem = Descriptions.Item;

  // 声明Emits
  const emit = defineEmits(['register', 'success']);

  // 客户记录
  interface CustomerRecord {
    id?: string;
    customerName?: string;
    gender?: number;
    mobile?: string;
    idType?: string;
    idNumber?: string;
    cusLevel?: string | number;
    cusLevelName?: string;
    cusLevel_dictText?: string;
    birthDate?: string;
    email?: string;
    occupation?: string;
    address?: string;
    realname?: string;
    jobPosition?: string;
    workUnit?: string;
    annualSalary?: number;
    customerStatus?: number;
    customerArea?: string;
    customerArea_dictText?: string;
    bankDeposit?: number;
    stockMarket?: number;
    realEstate?: number;
    antiques?: number;
    bonds?: number;
    otherAssets?: number;
    referrerName?: string;
    customerSource?: string;
    customerSource_dictText?: string;
    customerManagerName?: string;
    developerName?: string;
    createTime?: string;
    updateTime?: string;
    remark?: string;
    [key: string]: any;
  }

  const record = ref<CustomerRecord>({});

  // 计算总资产
  const calculateTotalAssets = () => {
    const assets = [
      record.value.bankDeposit || 0,
      record.value.stockMarket || 0,
      record.value.realEstate || 0,
      record.value.antiques || 0,
      record.value.bonds || 0,
      record.value.otherAssets || 0
    ];
    const total = assets.reduce((sum, asset) => sum + Number(asset), 0);
    return total.toFixed(2);
  };

  // 注册弹窗
  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });

    // 如果有记录ID，则查询详情
    if (data?.record?.id) {
      try {
        setModalProps({ confirmLoading: true });
        const result = await queryById({ id: data.record.id });
        record.value = result;
      } finally {
        setModalProps({ confirmLoading: false });
      }
    } else if (data?.record) {
      // 直接使用传入的记录
      record.value = { ...data.record };
    }
  });
</script>

<style scoped>
.customer-detail-container {
  max-height: 75vh;
  overflow-y: auto;
  padding: 0 8px;
}

.detail-section {
  margin-bottom: 24px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  overflow: hidden;
}

.detail-section:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.section-icon {
  font-size: 20px;
  margin-right: 10px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  flex: 1;
}

.total-assets {
  font-size: 16px;
  font-weight: 500;
}

.total-amount {
  font-size: 18px;
  font-weight: bold;
  color: #52c41a;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.section-content {
  padding: 24px;
  background: #ffffff;
}

/* 资产网格布局 */
.asset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.asset-item {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.asset-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: #1890ff;
}

.asset-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.asset-value {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}

/* 描述列表样式优化 */
.section-content :deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #262626;
  background-color: #f8f9fa;
}

.section-content :deep(.ant-descriptions-item-content) {
  color: #595959;
}

.section-content :deep(.ant-descriptions-bordered .ant-descriptions-item-label),
.section-content :deep(.ant-descriptions-bordered .ant-descriptions-item-content) {
  padding: 12px 16px;
}

/* 标签样式 */
.section-content :deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 12px;
}

/* 滚动条样式 */
.customer-detail-container::-webkit-scrollbar {
  width: 6px;
}

.customer-detail-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.customer-detail-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.customer-detail-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .customer-detail-container {
    max-height: 65vh;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .asset-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .asset-item {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .total-assets {
    align-self: flex-end;
  }
  
  .asset-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
