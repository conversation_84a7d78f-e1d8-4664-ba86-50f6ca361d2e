//import { BasicColumn } from '/@/components/Table';
import { FormSchema, BasicColumn } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getPinyin } from '/@/api/crmfy/crmPolicies.api';

// 列表页表格列定义
export const columns: BasicColumn[] = [
  {
    title: '保单号',
    dataIndex: 'policyNo',
    width: 150,
  },
  {
    title: '客户',
    dataIndex: 'customerName',
    width: 120,
    slots: { customRender: 'customerName' },
  },
  {
    title: '客户编号',
    dataIndex: 'customerNo',
    width: 120,
    slots: { customRender: 'customerNo' },
  },
  {
    title: '转介人',
    dataIndex: 'salesUsername_dictText',
    width: 120,
  },
  {
    title: '保险产品名称',
    dataIndex: 'productName',
    width: 180,
  },
  {
    title: '保险产品名称(英文)',
    dataIndex: 'planName',
    width: 180,
  },
  {
    title: '供应商',
    dataIndex: 'supplierNo_dictText',
    width: 120,
  },
  {
    title: '保单状态',
    dataIndex: 'policyStatus_dictText',
    width: 120,
  },
  {
    title: '预约时间',
    dataIndex: 'bookTime',
    width: 120,
  },
  {
    title: '签单地址',
    dataIndex: 'signAddr',
    width: 120,
  },
  {
    title: '缴费金额',
    dataIndex: 'paymentAmount',
    width: 120,
  },
  {
    title: '缴费频率',
    dataIndex: 'paymentFrequency_dictText',
    width: 100,
  },
  {
    title: '缴费年期',
    dataIndex: 'paymentTerm',
    width: 100,
  },
  {
    title: '通知电邮地址',
    dataIndex: 'notificationEmail',
    width: 180,
  },
  {
    title: '缴费方式',
    dataIndex: 'paymentMethod_dictText',
    width: 100,
  },
  {
    title: '缴费币种',
    dataIndex: 'paymentCurrency_dictText',
    width: 100,
  },
  {
    title: '销售员',
    dataIndex: 'salesUsername_dictText',
    width: 120,
  },
];

// 搜索表单定义
export const searchFormSchema: FormSchema[] = [
  {
    field: 'customerId',
    label: '客户',
    component: 'JSelectCustomer',
    componentProps: {
      placeholder: '请输入客户姓名或手机号搜索',
    },
    colProps: { span: 6 },
  },
  {
    field: 'policyNo',
    label: '保单号码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'planName',
    label: '产品名称(英文)',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'policyStatus',
    label: '保单状态',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'policy_status',
      placeholder: '请选择保单状态',
    },
    colProps: { span: 6 },
  },
  {
    field: 'productName',
    label: '保险产品名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'idNumber',
    label: '证件号码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'mobile',
    label: '手机号码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'supplierNo',
    label: '供应商',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
      placeholder: '请选择供应商',
    },
    colProps: { span: 6 },
  },
  {
    field: 'izActive',
    label: '是否最新版本',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
      placeholder: '是否最新版本',
    },
    defaultValue: '1',
    colProps: { span: 6 },
  },
];

// 基础表单字段（投保人、被保人、受益人共用）
const personFormSchema: FormSchema[] = [
  {
    field: 'lastNameCn',
    label: '姓',
    component: 'Input',
    required: true,
    componentProps: ({ formActionType, formModel }) => {
      return {
        placeholder: '请输入姓',
        onBlur: async (e: any) => {
          const lastNameCn = e.target.value;
          if (lastNameCn) {// && firstNameCn
            try {
              const result1 = await getPinyin(lastNameCn.trim());
              if (result1) {
                formModel.lastName = result1;
                formModel.namePy = result1+(formModel.firstName || "");
              }

            } catch (error) {
              console.error('获取拼音失败:', error);
            }
          }
        },
      };
    },
  },
  {
    field: 'firstNameCn',
    label: '名',
    component: 'Input',
    required: true,
    componentProps: ({ formActionType, formModel }) => {
      return {
        placeholder: '请输入名',
        onBlur: async (e: any) => {
          const firstNameCn = e.target.value;
          if (firstNameCn) {//lastNameCn && 
            try {
              
              const result = await getPinyin(firstNameCn.trim());
              if (result) {
                formModel.firstName = result;
                formModel.namePy =( formModel.lastName || "")+result;
              }
               

            } catch (error) {
              console.error('获取拼音失败:', error);
            }
          }
        },
      };
    },
  },
  {
    field: 'lastName',
    label: '英文姓',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
    componentProps: {
      placeholder: '根据中文姓自动生成',
    },
  },
  {
    field: 'firstName',
    label: '英文名',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
    componentProps: {
      placeholder: '根据中文名自动生成',
    },
  },
  {
    field: 'name',
    label: '中文姓名(保留)',
    component: 'Input',
    show: false,
    required: false,
  },
  {
    field: 'nameEng',
    label: '英文姓名(保留)',
    component: 'Input',
    show: false,
    required: false,
  },
  {
    field: 'namePy',
    label: '姓名拼音',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'nationality',
    label: '国籍',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'nationality',
    },
    required: false,
  },
  {
    field: 'idType',
    label: '证件类型',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'id_type',
    },
    required: true,
  },
  {
    field: 'idNumber',
    label: '证件号码',
    component: 'Input',
    required: true,
  },
  {
    field: 'idStartDate',
    label: '证件有效期起始',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: '请选择证件有效期起始日期',
    },
    required: false,
  },
  {
    field: 'idEndDate',
    label: '证件有效期截止',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: '请选择证件有效期截止日期',
    },
    required: false,
  },
  {
    field: 'idAddress',
    label: '住宅地址',
    component: 'InputTextArea',
    required: false,
  },
  {
    field: 'contactAddress',
    label: '通讯地址',
    component: 'InputTextArea',
    required: false,
  },
  {
    field: 'mobileGroup',
    label: '手机号码',
    component: 'Input',
    slot: 'mobileGroup',
    required: true,
    rules: [
      {
        required: true,
        validator: (_rule, value) => {
          if (!value) {
            return Promise.reject('请输入手机号码');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
  },
  {
    field: 'callingCode',
    label: '国际区号',
    component: 'JDictSelectTag',
    show: false,
    componentProps: {
      dictCode: 'calling_code',
    },
    required: true,
  },
  {
    field: 'mobile',
    label: '手机号码',
    component: 'Input',
    show: false,
    required: false,
  },
  {
    field: 'occupation',
    label: '工作职位',
    component: 'Input',
    required: false,
  },
  {
    field: 'companyAddress',
    label: '单位地址',
    component: 'InputTextArea',
    required: false,
  },
  {
    field: 'email',
    label: '电子邮箱',
    component: 'Input',
    required: false,
    rules: [
      {
        required: true,
        message: '请输入电子邮箱地址',
      },
      {
        type: 'email',
        message: '请输入正确的电子邮箱地址',
      },
      {
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        message: '请输入有效的电子邮箱地址',
      },
    ],
  },
  {
    field: 'bankAccountName',
    label: '银行开户名',
    component: 'Input',
    required: false,
  },
  {
    field: 'bankName',
    label: '银行名称',
    component: 'Input',
    required: false,
  },
  {
    field: 'bankCurrency',
    label: '账户币种',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_currency',
    },
    required: false,
  },
  {
    field: 'bankAccount',
    label: '银行账号',
    component: 'Input',
    required: false,
  },
  {
    field: 'high',
    label: '身高(cm)',
    component: 'InputNumber',
    componentProps: {
      min: 50,
      max: 250,
      precision: 2,
      placeholder: '请输入身高',
    },
    required: false,
  },
  {
    field: 'weight',
    label: '体重(kg)',
    component: 'InputNumber',
    componentProps: {
      min: 10,
      max: 300,
      precision: 2,
      placeholder: '请输入体重',
    },
    required: false,
  },
  {
    field: 'izSmoking',
    label: '是否吸烟',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
      placeholder: '请选择是否吸烟',
    },
    required: false,
  },
  {
    field: 'izMarried',
    label: '是否已婚',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
      placeholder: '请选择婚姻状况',
    },
    required: false,
  },
  {
    field: 'workDetail',
    label: '工作内容',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请详细描述工作内容',
      rows: 3,
    },
    required: false,
  },
  {
    field: 'annualIncome',
    label: '年薪(人民币)',
    component: 'Input',
    componentProps: {
      placeholder: '请输入年薪',
    },
    required: false,
  },
  {
    field: 'deposit',
    label: '存款',
    component: 'Input',
    componentProps: {
      placeholder: '请输入存款金额',
    },
    required: false,
  },
  {
    field: 'stock',
    label: '股票',
    component: 'Input',
    componentProps: {
      placeholder: '请输入股票资产',
    },
    required: false,
  },
  {
    field: 'house',
    label: '房产',
    component: 'Input',
    componentProps: {
      placeholder: '请输入房产信息',
    },
    required: false,
  },
  {
    field: 'oldThing',
    label: '古玩字画',
    component: 'Input',
    componentProps: {
      placeholder: '请输入古玩字画资产',
    },
    required: false,
  },
  {
    field: 'otherMoney',
    label: '其他资产',
    component: 'Input',
    componentProps: {
      placeholder: '请输入其他资产',
    },
    required: false,
  },
];

// 保单表单定义
export const policyFormSchema: FormSchema[] = [

  {
    field: 'bookTime',
    label: '预约时间',
     component: 'DatePicker',
    componentProps: {
      placeholder: '请选择预约时间',
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    ifShow: ({ values }) => {
      return values.policyStatus === '0' || values.policyStatus === undefined ;
    },
    required: ({ values }) => {
      return values.policyStatus === '0' || values.policyStatus === undefined ;
    },
  },
  {
    field: 'signAddr',
    label: '签单地址',
    component: 'InputTextArea',
    ifShow: ({ values }) => {
      return values.policyStatus === '0' || values.policyStatus === undefined ;
    },
    required: ({ values }) => {
      return values.policyStatus === '0' || values.policyStatus === undefined;
    },
  },
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'isNewCustomer',
    label: '是否新客户',
    component: 'Switch',
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
    },
    defaultValue: true,
    required: true,
  },
  {
    field: 'customerId',
    label: '客户',
    component: 'JSelectCustomer',
    slot: 'customerId',
    componentProps: {
      placeholder: '请输入客户姓名或手机号搜索',
    },
    ifShow: ({ values }) => {
      // 当选择否（非新客户）时显示客户选择
      return !values.isNewCustomer;
    },
    required: ({ values }) => {
      // 当选择否（非新客户）时必填
      return !values.isNewCustomer;
    },
  },
  {
    field: 'policyHolderInfo',
    label: '投保人信息',
    component: 'Input',
    slot: 'policyHolderInfo',
    colProps: { span: 24 }, // 设置为一行一列
    required: true, // 设置为必填项
  },
  {
    field: 'insuredInfo',
    label: '被保人信息',
    component: 'Input',
    slot: 'insuredInfo',
    colProps: { span: 24 }, // 设置为一行一列
    required: true, // 设置为必填项
  },
  {
    field: 'beneficiaryInfo',
    label: '受益人信息',
    component: 'Input',
    slot: 'beneficiaryInfo',
    colProps: { span: 24 }, // 设置为一行一列
    required: true, // 设置为必填项
  },
  {
    field: 'customerSource',
    label: '客户区域',
    component: 'Input',
    show: false,
  },
  {
    field: 'supplierNo',
    label: '供应商',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
    },
    required: true,
  },
  {
    field: 'productType',
    label: '产品类型',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'product_category',
    },
    required: true,
  },

  {
    field: 'productId',
    label: '保险产品',
    component: 'JSelectProduct',
    slot: 'productId',
    required: true,
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    dynamicDisabled: false,
    show: false,
  },
  {
    field: 'planName',
    label: '产品名称(英文)',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'policyNo',
    label: '保单号码',
    component: 'Input',
    required: ({ values }) => {
      // 当核保结果为不通过(0)时必填
      return values.policyStatus === '6';
    },
  },
  {
    field: 'paymentAmount',
    label: '缴费金额',
    component: 'InputNumber',
    required: true,
  },
  {
    field: 'paymentFrequency',
    label: '缴费频率', //根据所选产品查询产品的缴费频率列表
    component: 'Select',
    componentProps: ({ formModel }) => {
      console.log('CrmPolicies.data.ts中的formModel:', formModel);
      // 如果客户区域是大陆客户(1)，则只能选择年缴
      // 强制转换为字符串进行比较
      const customerSource = String(formModel.customerSource || '');
      console.log('CrmPolicies.data.ts中的客户区域:', customerSource, '是否等于"1":', customerSource === '1');
      if (customerSource === '1') {
        return {
          placeholder: '大陆客户只能选择年缴',
          options: [{ label: '年缴', value: '4' }],
          disabled: false,
        };
      } else {
        // 其他客户区域，显示产品支持的所有缴费频率
        return {
          placeholder: '请先选择产品',
          options: formModel.paymentFrequencyOptions || [],
          disabled: !formModel.paymentFrequencyOptions || formModel.paymentFrequencyOptions.length === 0,
        };
      }
    },
    required: true,
  },
  {
    field: 'paymentTerm',
    label: '缴费年期', //根据所选产品查询产品的缴费年期列表
    component: 'Select',
    componentProps: {
      placeholder: '请先选择产品',
      options: [],
      disabled: true,
    },
    required: true,
  },
  {
    field: 'paymentCurrency',
    label: '缴费币种', //根据所选产品查询产品的支持币种列表
    component: 'Select',
    componentProps: {
      placeholder: '请先选择产品',
      options: [],
      disabled: true,
    },
    required: true,
  },
  {
    field: 'paymentMethod',
    label: '缴费方式',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_method',
    },
    required: true,
  },
  {
    field: 'coveragePeriod',
    label: '保障期限',
    component: 'Input',
    required: true,
  },
  {
    field: 'autoDebit',
    label: '是否自动扣款',
    component: 'Switch',
    defaultValue: false,
  },
  {
    field: 'debitDate',
    label: '扣款日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择扣款日期',
      format: 'YYYY-MM-DD',
    },
    ifShow: ({ values }) => {
      // 当自动扣款选择是时显示
      return values.autoDebit === true;
    },
    required: ({ values }) => {
      // 当自动扣款选择是时必填
      return values.autoDebit === true;
    },
  },

  {
    field: 'promotionPlan',
    label: '优惠方案',
    component: 'Input',
  },
  {
    field: 'notificationEmail',
    label: '通知电邮地址',
    component: 'Input',
    required: true,
    rules: [
      {
        required: true,
        message: '请输入电子邮箱地址',
      },
      {
        type: 'email',
        message: '请输入正确的电子邮箱地址',
      },
      {
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        message: '请输入有效的电子邮箱地址',
      },
    ],
  },

  {
    field: 'rcExchangeRate',
    label: '当天汇率(兑港元)',
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0,
    },
    ifShow: ({ values }) => {
      // 当保单状态为保单签发(7)时显示
      return values.policyStatus === '7' || values.policyStatus === '6' || values.policyStatus === '9' || values.policyStatus === '8';
    },
    required: ({ values }) => {
      // 当保单状态为保单签发(7)时必填
      return values.policyStatus === '7' || values.policyStatus === '6' || values.policyStatus === '9' || values.policyStatus === '8';
    },
  },
  {
    field: 'policyIssueDate',
    label: '保单生效日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择保单生效日期',
      format: 'YYYY-MM-DD',
    },
    ifShow: ({ values }) => {
      // 当保单状态为保单签发(7)时显示
      return values.policyStatus === '7' || values.policyStatus === '6' || values.policyStatus === '9' || values.policyStatus === '8';
    },
    required: ({ values }) => {
      // 当保单状态为保单签发(7)时必填
      return values.policyStatus === '7' || values.policyStatus === '6' || values.policyStatus === '9' || values.policyStatus === '8';
    },
  },
  {
    field: 'coolingOffDate',
    label: '冷静期到期日',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择冷静期到期日',
      format: 'YYYY-MM-DD',
    },
    ifShow: ({ values }) => {
      // 当保单状态为保单签发(7)时显示
      return values.policyStatus === '7' || values.policyStatus === '6' || values.policyStatus === '9' || values.policyStatus === '8';
    },
    required: ({ values }) => {
      // 当保单状态为保单签发(7)时必填
      return values.policyStatus === '7' || values.policyStatus === '6' || values.policyStatus === '9' || values.policyStatus === '8';
    },
  },

  {
    field: 'policyStatus',
    label: '保单状态',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'policy_status',
    },
    dynamicDisabled: true,
  },

  {
    field: 'auditResult',
    label: '核保是否通过',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
    },
    // 默认隐藏，由Modal中的updateFormFieldsConfig动态控制
    ifShow: false,
    required: false,
  },

  {
    field: 'noticeRemark',
    label: '照会说明',
    component: 'InputTextArea',
    dynamicDisabled: ({ values }) => {
      // 当销售员照会回复时，照会说明不可编辑
      return values.policyStatus === '3';
    },
    // 仅在核保不通过时显示且必填
    ifShow: ({ values }) => {
      // 当核保结果为不通过(0)时显示
      return values.auditResult === '0' || values.policyStatus === '3';
    },
    required: ({ values }) => {
      // 当核保结果为不通过(0)时必填
      return values.auditResult === '0';
    },
  },
  {
    field: 'noticeReply',
    label: '照会回复',
    component: 'InputTextArea',
    dynamicDisabled: false,
    // 默认隐藏，由Modal中的updateFormFieldsConfig动态控制
    ifShow: false,
    required: false,
  },
  {
    field: 'changeReason',
    label: '变更原因',
    component: 'InputTextArea',
    dynamicDisabled: false,
    // 默认隐藏，由Modal中的updateFormFieldsConfig动态控制
    ifShow: false,
    required: false,
  },

  {
    field: 'additionalInsurance',
    label: '附加险',
    component: 'Input',
    slot: 'additionalInsurance',
    colProps: { span: 24 }, // 设置为一行一列
  },
  {
    field: 'signatureSms',
    label: '签字链接',
    component: 'Input',
    // 隐藏字段，仅用于数据存储
    ifShow: false,
  },
  {
    field: 'signatureStatus',
    label: '签字状态',
    component: 'Input',
    // 隐藏字段，仅用于数据存储
    ifShow: false,
  }
];

// 导出投保人表单 - 添加与投保人关系字段和新增字段
export const policyHolderFormSchema: FormSchema[] = [
  ...personFormSchema,
  {
    field: 'companyName',
    label: '僱主名稱',
    component: 'Input',
    required: false,
  } as FormSchema,
  {
    field: 'trade',
    label: '行業',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'trade',
    },
    required: false,
  } as FormSchema,
  {
    field: 'education',
    label: '教育程度',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'education',
    },
    required: false,
  } as FormSchema,
  {
    field: 'gender',
    label: '性別',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sex',
    },
    required: false,
  } as FormSchema,
  {
    field: 'birthDay',
    label: '出生日',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    required: false,
  } as FormSchema,
];

// 导出被保人表单 - 添加与投保人关系字段和新增字段
export const insuredFormSchema: FormSchema[] = [
  ...personFormSchema.slice(0, 3), // 包含name, nameEng, namePy
  {
    field: 'withRelation',
    label: '与投保人关系',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'with_relation',
      placeholder: '请选择关系',
    },
    required: true,
  } as FormSchema,
  ...personFormSchema.slice(3), // 从nationality开始的其他字段
  {
    field: 'companyName',
    label: '僱主名稱',
    component: 'Input',
    required: false,
  } as FormSchema,
  {
    field: 'trade',
    label: '行業',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'trade',
    },
    required: false,
  } as FormSchema,
  {
    field: 'education',
    label: '教育程度',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'education',
    },
    required: false,
  } as FormSchema,
  {
    field: 'gender',
    label: '性別',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sex',
    },
    required: false,
  } as FormSchema,
  {
    field: 'birthDay',
    label: '出生日',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    required: false,
  } as FormSchema,
];

// 导出受益人表单 - 添加与被保人关系字段
export const beneficiaryFormSchema: FormSchema[] = [
  ...personFormSchema.slice(0, 3), // 包含name, nameEng, namePy
  {
    field: 'withRelation',
    label: '与被保人关系',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'with_relation',
      placeholder: '请选择关系',
    },
    required: true,
  } as FormSchema,
  {
    field: 'benefitRatio',
    label: '受益比例(%)',
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 100,
      precision: 2,
      placeholder: '请输入受益比例(1-100)',
    },
    required: true,
    helpMessage: '受益比例必须在1-100之间，多个受益人比例之和必须为100%',
  } as FormSchema,
  ...personFormSchema.slice(3), // 从nationality开始的其他字段
];

// 附加险表单
export const additionalInsuranceFormSchema: FormSchema[] = [
  {
    field: 'supplierNo',
    label: '供应商',
    component: 'Input',
    show: false, // 隐藏字段，仅用于传递供应商编码
  },
  {
    field: 'productType',
    label: '产品类型',
    component: 'Input',
    show: false, // 隐藏字段，仅用于传递产品类型
  },
  {
    field: 'productId',
    label: '附加险产品',
    component: 'JSelectProduct',
    slot: 'productId', // 使用插槽渲染，确保能正确接收supplierNo
    required: true,
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'planName',
    label: '产品名称(英文)',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'additionalAmount',
    label: '每期保费',
    component: 'InputNumber',
    required: true,
  },
];
