<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="80%" :showOkBtn="false">
    <a-spin :spinning="loading">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="基本信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="保单号">{{ policyDetail.policyNo }}</a-descriptions-item>
            <a-descriptions-item label="客户">{{ policyDetail.customerId_dictText}}</a-descriptions-item>
            <a-descriptions-item label="保险产品名称">{{ policyDetail.productName }}</a-descriptions-item>
            <a-descriptions-item label="保险产品名称(英文)">{{ policyDetail.planName }}</a-descriptions-item>
            <a-descriptions-item label="供应商">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.SUPPLIER, policyDetail.supplierNo) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="保单状态">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.POLICY_STATUS, policyDetail.policyStatus) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="缴费金额">{{ policyDetail.paymentAmount }}</a-descriptions-item>
            <a-descriptions-item label="缴费频率">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.PAYMENT_FREQUENCY, policyDetail.paymentFrequency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="缴费年期">{{ policyDetail.paymentTerm }}</a-descriptions-item>
            <a-descriptions-item label="通知电邮地址">{{ policyDetail.notificationEmail }}</a-descriptions-item>
            <a-descriptions-item label="缴费方式">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.PAYMENT_METHOD, policyDetail.paymentMethod) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="缴费币种">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.PAYMENT_CURRENCY, policyDetail.paymentCurrency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="订单状态">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.POLICY_STATUS, policyDetail.policyStatus) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="销售员">{{ policyDetail.salesUsername_dictText }}</a-descriptions-item>
            <a-descriptions-item label="优惠方案">{{ policyDetail.promotionPlan }}</a-descriptions-item>
            <a-descriptions-item label="是否自动扣款">
              <a-tag :color="policyDetail.autoDebit ? 'green' : 'red'">
                {{ policyDetail.autoDebit ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="保障期限">{{ policyDetail.coveragePeriod }}</a-descriptions-item>
            <a-descriptions-item label="佣金结算时汇率">{{ policyDetail.rcExchangeRate }}</a-descriptions-item>
            <!-- 签名链接 -->
            <a-descriptions-item label="签字链接">
              <template v-if="policyDetail.signatureSms">
                <a :href="policyDetail.signatureSms" target="_blank">
                  {{ policyDetail.signatureSms }}
                </a>
              </template>
              <template v-else>无</template>
            </a-descriptions-item>
          </a-descriptions>

          <!-- 附加险列表 -->
          <a-divider orientation="left">附加险列表</a-divider>
          <a-table
            :dataSource="policyDetail.crmPolicyAdditionals || []"
            :columns="additionalColumns"
            :pagination="false"
            bordered
            size="small"
          />
        </a-tab-pane>

        <a-tab-pane key="2" tab="投保人信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="姓">{{ policyDetail.crmPolicyHolders?.lastNameCn }}</a-descriptions-item>
            <a-descriptions-item label="名">{{ policyDetail.crmPolicyHolders?.firstNameCn }}</a-descriptions-item>
            <a-descriptions-item label="英文姓">{{ policyDetail.crmPolicyHolders?.lastName }}</a-descriptions-item>
            <a-descriptions-item label="英文名">{{ policyDetail.crmPolicyHolders?.firstName }}</a-descriptions-item>
            <a-descriptions-item label="姓名(完整)">{{ policyDetail.crmPolicyHolders?.name }}</a-descriptions-item>
            <a-descriptions-item label="英文姓名(完整)">{{ policyDetail.crmPolicyHolders?.nameEng }}</a-descriptions-item>
            <a-descriptions-item label="国籍">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.NATIONALITY, policyDetail.crmPolicyHolders?.nationality) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件类型">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.ID_TYPE, policyDetail.crmPolicyHolders?.idType) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件号码">{{ policyDetail.crmPolicyHolders?.idNumber }}</a-descriptions-item>
            <a-descriptions-item label="证件有效期起始">{{ policyDetail.crmPolicyHolders?.idStartDate }}</a-descriptions-item>
            <a-descriptions-item label="证件有效期截止">{{ policyDetail.crmPolicyHolders?.idEndDate }}</a-descriptions-item>
            <a-descriptions-item label="住宅地址" :span="2">{{ policyDetail.crmPolicyHolders?.idAddress }}</a-descriptions-item>
            <a-descriptions-item label="通讯地址" :span="2">{{ policyDetail.crmPolicyHolders?.contactAddress }}</a-descriptions-item>
            <a-descriptions-item label="手机号码">{{ policyDetail.crmPolicyHolders?.callingCode }} {{ policyDetail.crmPolicyHolders?.mobile }}</a-descriptions-item>
            <a-descriptions-item label="工作职位">{{ policyDetail.crmPolicyHolders?.occupation }}</a-descriptions-item>
            <a-descriptions-item label="单位地址" :span="2">{{ policyDetail.crmPolicyHolders?.companyAddress }}</a-descriptions-item>
            <a-descriptions-item label="电子邮箱">{{ policyDetail.crmPolicyHolders?.email }}</a-descriptions-item>
            <a-descriptions-item label="银行开户名">{{ policyDetail.crmPolicyHolders?.bankAccountName }}</a-descriptions-item>
            <a-descriptions-item label="银行名称">{{ policyDetail.crmPolicyHolders?.bankName }}</a-descriptions-item>
            <a-descriptions-item label="账户币种">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.PAYMENT_CURRENCY, policyDetail.crmPolicyHolders?.bankCurrency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="银行账号">{{ policyDetail.crmPolicyHolders?.bankAccount }}</a-descriptions-item>
            <a-descriptions-item label="身高(cm)">{{ policyDetail.crmPolicyHolders?.high }}</a-descriptions-item>
            <a-descriptions-item label="体重(kg)">{{ policyDetail.crmPolicyHolders?.weight }}</a-descriptions-item>
            <a-descriptions-item label="是否吸烟">
              <a-tag color="blue">{{ policyDetail.crmPolicyHolders?.izSmoking === '1' ? '是' : '否' }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="是否已婚">
              <a-tag color="blue">{{ policyDetail.crmPolicyHolders?.izMarried === '1' ? '是' : '否' }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="工作内容" :span="2">{{ policyDetail.crmPolicyHolders?.workDetail }}</a-descriptions-item>
            <a-descriptions-item label="年薪(人民币)">{{ policyDetail.crmPolicyHolders?.annualIncome }}</a-descriptions-item>
            <a-descriptions-item label="存款">{{ policyDetail.crmPolicyHolders?.deposit }}</a-descriptions-item>
            <a-descriptions-item label="股票">{{ policyDetail.crmPolicyHolders?.stock }}</a-descriptions-item>
            <a-descriptions-item label="房产">{{ policyDetail.crmPolicyHolders?.house }}</a-descriptions-item>
            <a-descriptions-item label="古玩字画">{{ policyDetail.crmPolicyHolders?.oldThing }}</a-descriptions-item>
            <a-descriptions-item label="其他资产">{{ policyDetail.crmPolicyHolders?.otherMoney }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>

        <a-tab-pane key="3" tab="被保人信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="姓">{{ policyDetail.crmPolicyInsureds?.lastNameCn }}</a-descriptions-item>
            <a-descriptions-item label="名">{{ policyDetail.crmPolicyInsureds?.firstNameCn }}</a-descriptions-item>
            <a-descriptions-item label="英文姓">{{ policyDetail.crmPolicyInsureds?.lastName }}</a-descriptions-item>
            <a-descriptions-item label="英文名">{{ policyDetail.crmPolicyInsureds?.firstName }}</a-descriptions-item>
            <a-descriptions-item label="姓名(完整)">{{ policyDetail.crmPolicyInsureds?.name }}</a-descriptions-item>
            <a-descriptions-item label="英文姓名(完整)">{{ policyDetail.crmPolicyInsureds?.nameEng }}</a-descriptions-item>
            <a-descriptions-item label="与投保人关系">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.WITH_RELATION, policyDetail.crmPolicyInsureds?.withRelation) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="国籍">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.NATIONALITY, policyDetail.crmPolicyInsureds?.nationality) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件类型">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.ID_TYPE, policyDetail.crmPolicyInsureds?.idType) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件号码">{{ policyDetail.crmPolicyInsureds?.idNumber }}</a-descriptions-item>
            <a-descriptions-item label="证件有效期起始">{{ policyDetail.crmPolicyInsureds?.idStartDate }}</a-descriptions-item>
            <a-descriptions-item label="证件有效期截止">{{ policyDetail.crmPolicyInsureds?.idEndDate }}</a-descriptions-item>
            <a-descriptions-item label="住宅地址" :span="2">{{ policyDetail.crmPolicyInsureds?.idAddress }}</a-descriptions-item>
            <a-descriptions-item label="通讯地址" :span="2">{{ policyDetail.crmPolicyInsureds?.contactAddress }}</a-descriptions-item>
            <a-descriptions-item label="手机号码">{{ policyDetail.crmPolicyInsureds?.callingCode}} {{ policyDetail.crmPolicyInsureds?.mobile }}</a-descriptions-item>
            <a-descriptions-item label="工作职位">{{ policyDetail.crmPolicyInsureds?.occupation }}</a-descriptions-item>
            <a-descriptions-item label="单位地址" :span="2">{{ policyDetail.crmPolicyInsureds?.companyAddress }}</a-descriptions-item>
            <a-descriptions-item label="电子邮箱">{{ policyDetail.crmPolicyInsureds?.email }}</a-descriptions-item>
            <a-descriptions-item label="银行开户名">{{ policyDetail.crmPolicyInsureds?.bankAccountName }}</a-descriptions-item>
            <a-descriptions-item label="银行名称">{{ policyDetail.crmPolicyInsureds?.bankName }}</a-descriptions-item>
            <a-descriptions-item label="账户币种">
              <a-tag color="blue">{{ getDictText(DICT_TYPES.PAYMENT_CURRENCY, policyDetail.crmPolicyInsureds?.bankCurrency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="银行账号">{{ policyDetail.crmPolicyInsureds?.bankAccount }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>

        <a-tab-pane key="4" tab="受益人信息">
          <div v-if="!policyDetail.crmPolicyBeneficiaries || policyDetail.crmPolicyBeneficiaries.length === 0">
            暂无受益人信息
          </div>
          <div v-else>
            <a-divider orientation="left">受益人列表</a-divider>
            <a-table
              :dataSource="policyDetail.crmPolicyBeneficiaries || []"
              :columns="beneficiaryColumns"
              :pagination="false"
              bordered
              size="small"
              rowKey="id"
            />
            
            <template v-for="(beneficiary, index) in policyDetail.crmPolicyBeneficiaries" :key="beneficiary.id || index">
              <a-divider orientation="left">受益人 {{ index + 1 }} - {{ beneficiary.name }} ({{ beneficiary.benefitRatio }}%)</a-divider>
              <a-descriptions bordered :column="2">
                <a-descriptions-item label="姓名">{{ beneficiary.name }}</a-descriptions-item>
                <a-descriptions-item label="英文姓名">{{ beneficiary.nameEng }}</a-descriptions-item>
                <a-descriptions-item label="与被保人关系">
                  <a-tag color="blue">{{ getDictText(DICT_TYPES.WITH_RELATION, beneficiary.withRelation) }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="受益比例">{{ beneficiary.benefitRatio }}%</a-descriptions-item>
                <a-descriptions-item label="国籍">
                  <a-tag color="blue">{{ getDictText(DICT_TYPES.NATIONALITY, beneficiary.nationality) }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="证件类型">
                  <a-tag color="blue">{{ getDictText(DICT_TYPES.ID_TYPE, beneficiary.idType) }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="证件号码">{{ beneficiary.idNumber }}</a-descriptions-item>
                <a-descriptions-item label="住宅地址" :span="2">{{ beneficiary.idAddress }}</a-descriptions-item>
                <a-descriptions-item label="通讯地址" :span="2">{{ beneficiary.contactAddress }}</a-descriptions-item>
                <a-descriptions-item label="手机号码">{{ beneficiary.callingCode }} {{ beneficiary.mobile }}</a-descriptions-item>
                <a-descriptions-item label="工作职位">{{ beneficiary.occupation }}</a-descriptions-item>
                <a-descriptions-item label="单位地址" :span="2">{{ beneficiary.companyAddress }}</a-descriptions-item>
                <a-descriptions-item label="电子邮箱">{{ beneficiary.email }}</a-descriptions-item>
                <a-descriptions-item label="银行开户名">{{ beneficiary.bankAccountName }}</a-descriptions-item>
                <a-descriptions-item label="银行名称">{{ beneficiary.bankName }}</a-descriptions-item>
                <a-descriptions-item label="账户币种">
                  <a-tag color="blue">{{ getDictText(DICT_TYPES.PAYMENT_CURRENCY, beneficiary.bankCurrency) }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="银行账号">{{ beneficiary.bankAccount }}</a-descriptions-item>
              </a-descriptions>
            </template>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="5" tab="订单相关文件">
          <div v-if="!policyDetail.crmPolicyFiles || policyDetail.crmPolicyFiles.length === 0">
            暂无订单文件
          </div>
          <div v-else>
            <a-table
              :dataSource="policyDetail.crmPolicyFiles || []"
              :columns="fileColumns"
              :pagination="false"
              bordered
              size="small"
              rowKey="id"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <!-- <a-button type="link" @click="handleFilePreview(record)">预览</a-button> -->
                  <a-button type="link" @click="handleFileDownload(record)">下载</a-button>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, h, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getPolicyDetail } from '/@/api/crmfy/crmPolicies.api';
  import { CrmPolicyDTO } from '/@/types/crmfy/crmPolicies';
  import { message, Tag } from 'ant-design-vue';
  import { getDictItemsByCode, initDictOptions, getDictText, useGlobalDictRefreshKey } from '/@/utils/dict/index';
  import { useUserStore } from '/@/store/modules/user';
  import { useGlobSetting } from '/@/hooks/setting';
  import { encryptByBase64 } from '/@/utils/cipher';

  // 字典类型常量
  const DICT_TYPES = {
    SUPPLIER: 'supplier',
    POLICY_STATUS: 'policy_status',
    PAYMENT_FREQUENCY: 'payment_frequency',
    PAYMENT_METHOD: 'payment_method',
    PAYMENT_CURRENCY: 'payment_currency',
    NATIONALITY: 'nationality',
    ID_TYPE: 'id_type',
    WITH_RELATION: 'with_relation',
    POLICY_FILE_TYPE: 'policy_file_type'
  } as const;

  // 文件大小单位常量
  const FILE_SIZE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB'] as const;

  // 定义附加险表格列
  const additionalColumns = [
    {
      title: '保险产品名称(英文)',
      dataIndex: 'planName',
      key: 'planName',
    },
    {
      title: '每期保费金额',
      dataIndex: 'additionalAmount',
      key: 'additionalAmount',
    },
  ];
  
  // 定义受益人表格列
  const beneficiaryColumns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '与被保人关系',
      dataIndex: 'withRelation',
      key: 'withRelation',
      customRender: ({ text }: { text: string }) => {
        return h(Tag, { color: 'blue' }, () => getDictText(DICT_TYPES.WITH_RELATION, text));
      }
    },
    {
      title: '受益比例(%)',
      dataIndex: 'benefitRatio',
      key: 'benefitRatio',
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      key: 'idNumber',
    },
  ];

  // 定义文件表格列
  const fileColumns = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '文件类型',
      dataIndex: 'fileType',
      key: 'fileType',
      customRender: ({ text }: { text: string }) => {
        // 添加响应式依赖
        globalDictRefreshKey.value;
        return getDictText(DICT_TYPES.POLICY_FILE_TYPE, text);
      }
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      customRender: ({ text }: { text: number | string }) => {
        return formatFileSize(text);
      }
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 定义变量
  const loading = ref<boolean>(false);
  const activeKey = ref<string>('1');
  const policyDetail = ref<CrmPolicyDTO>({});
  const glob = useGlobSetting();
  const userStore = useUserStore();

  // 设置标题
  const title = computed(() => '保单详情');

  // 使用全局字典翻译函数
  const globalDictRefreshKey = useGlobalDictRefreshKey();

  // 格式化文件大小
  const formatFileSize = (size: number | string | undefined): string => {
    const numSize = Number(size);
    if (!numSize || numSize <= 0) return '0 B';
    
    let index = 0;
    let fileSize = numSize;
    
    while (fileSize >= 1024 && index < FILE_SIZE_UNITS.length - 1) {
      fileSize /= 1024;
      index++;
    }
    
    return `${fileSize.toFixed(2)} ${FILE_SIZE_UNITS[index]}`;
  };
  
  // 文件预览
  const handleFilePreview = (record: any): void => {
    if (!record?.filePath) {
      message.error('文件路径不存在，无法预览');
      return;
    }
    
    try {
      const url = encodeURIComponent(encryptByBase64(record.filePath));
      const previewUrl = `${glob.viewUrl}?url=${url}`;
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('文件预览失败:', error);
      message.error('文件预览失败');
    }
  };
  
  // 文件下载
  const handleFileDownload = (record: any): void => {
    if (!record?.filePath) {
      message.error('文件路径不存在，无法下载');
      return;
    }
    
    try {
      const link = document.createElement('a');
      link.href = record.filePath;
      link.target = '_blank';
      link.download = record.fileName || '未命名文件';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('文件下载失败:', error);
      message.error('文件下载失败');
    }
  };

  // 注册弹窗
  const [registerModal] = useModalInner(async (data) => {
    // 重置数据
    policyDetail.value = {};
    activeKey.value = '1';

    // 加载详情数据
    if (data.record) {
      loading.value = true;
      try {
        const result = await getPolicyDetail(data.record.id);
        policyDetail.value = result || {};
      } catch (error) {
        console.error('获取保单详情失败', error);
        message.error('获取保单详情失败');
      } finally {
        loading.value = false;
      }
    }
  });
</script>

<style lang="less" scoped>
  :deep(.ant-descriptions-item-label) {
    width: 150px;
    font-weight: bold;
  }
</style>
