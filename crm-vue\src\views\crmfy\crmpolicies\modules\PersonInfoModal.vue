<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" :width="600" :height="500">
    
    <BasicForm @register="registerForm">
      <template #mobileGroup>
        <div style="display: flex; flex-direction: column">
          <div style="display: flex; align-items: center">
            <JDictSelectTag
              v-model:value="callingCodeValue"
              dictCode="calling_code"
              placeholder="区号"
              style="width: 100px; margin-right: 8px"
              @change="handleCallingCodeChange"
            />
            <span style="margin: 0 8px">
              <svg viewBox="64 64 896 896" focusable="false" data-icon="right" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
            <a-input
              v-model:value="mobileValue"
              placeholder="手机号码"
              style="flex: 1"
              @input="handleMobileInput"
              :status="mobileError ? 'error' : ''"
            />
          </div>
          <div v-if="mobileError" style="color: #ff4d4f; font-size: 12px; margin-top: 4px"> 请输入手机号码 </div>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, onMounted, watch, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { policyHolderFormSchema, insuredFormSchema, beneficiaryFormSchema } from '../CrmPolicies.data';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDictItemsByCode } from '/@/utils/dict/index';
  import { getPinyin } from '/@/api/crmfy/crmPolicies.api';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';

  // 声明Emits
  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();

  // 表单状态
  const isUpdate = ref(false);
  const record = ref<Recordable>({});
  const personType = ref('policyHolder'); // policyHolder, insured, beneficiary
  
  // 勾选框相关状态

  const customerInfo = ref<Recordable>({});
  const policyHolderInfo = ref<Recordable>({});

  // 区号和手机号码值
  const callingCodeValue = ref('');
  const mobileValue = ref('');

  // 手机号错误状态
  const mobileError = ref(false);

  // 处理区号变更
  function handleCallingCodeChange(value: string) {
    callingCodeValue.value = value;
    updateFormValues();
  }

  // 处理手机号输入
  function handleMobileInput(e: Event) {
    const value = (e.target as HTMLInputElement).value;
    mobileValue.value = value;
    // 更新验证状态
    mobileError.value = !value;
    updateFormValues();
  }

  // 更新表单值
  async function updateFormValues() {
    try {
      await setFieldsValue({
        callingCode: callingCodeValue.value,
        mobile: mobileValue.value,
        mobileGroup: `${callingCodeValue.value} → ${mobileValue.value}`,
      });
    } catch (error) {
      console.error('更新表单值失败:', error);
    }
  }



  // 复制源信息到表单
  async function copySourceInfo() {
    try {
      const type = unref(personType);
      let sourceData: Recordable = {};

      if (type === 'policyHolder') {
        // 投保人复制客户信息
        sourceData = unref(customerInfo);
      } else if (type === 'insured' || type === 'beneficiary') {
        // 被保人和受益人复制投保人信息
        sourceData = unref(policyHolderInfo);
      }

      if (sourceData && Object.keys(sourceData).length > 0) {
        // 设置区号和手机号
        if (sourceData.callingCode) {
          callingCodeValue.value = sourceData.callingCode;
        }
        if (sourceData.mobile) {
          mobileValue.value = sourceData.mobile;
        }
        // 复制其他字段到表单
        const formData: Recordable = {
          lastNameCn: sourceData.lastNameCn || '',
          firstNameCn: sourceData.firstNameCn || '',
          lastName: sourceData.lastName || '',
          firstName: sourceData.firstName || '',
          name: sourceData.name || sourceData.customerName || '',
          nameEng: sourceData.nameEng || '',
          namePy: sourceData.namePy || '',
          nationality: sourceData.nationality || '',
          idType: sourceData.idType || '',
          idNumber: sourceData.idNumber || '',
          idStartDate: sourceData.idStartDate || '',
          idEndDate: sourceData.idEndDate || '',
          idAddress: sourceData.idAddress || sourceData.address || '',
          contactAddress: sourceData.contactAddress || sourceData.address || '',
          callingCode: sourceData.callingCode || '+86',
          mobile: sourceData.mobile || '',
          occupation: sourceData.occupation || '',
          workDetail: sourceData.workDetail || '',
          companyAddress: sourceData.companyAddress || '',
          email: sourceData.email || '',
          bankAccountName: sourceData.bankAccountName || '',
          bankName: sourceData.bankName || '',
          bankCurrency: sourceData.bankCurrency || '',
          bankAccount: sourceData.bankAccount || '',
          companyName: sourceData.companyName || '',
          trade: sourceData.trade || '',
          education: sourceData.education || '',
          gender: sourceData.gender + '' || '',
          birthDay: sourceData.birthDay || sourceData.birthDate || '',
          high: sourceData.high || '',
          weight: sourceData.weight || '',
          izSmoking: sourceData.izSmoking || '',
          izMarried: sourceData.izMarried || '',
          annualIncome: sourceData.annualIncome || '',
          deposit: sourceData.deposit || '',
          stock: sourceData.stock || '',
          house: sourceData.house || '',
          oldThing: sourceData.oldThing || '',
          otherMoney: sourceData.otherMoney || '',
        };

        // 更新手机号组合显示
        if (formData.callingCode && formData.mobile) {
          formData.mobileGroup = `${formData.callingCode} → ${formData.mobile}`;
        }

        await setFieldsValue(formData);
        
        // 如果复制了中文姓名，手动触发拼音生成
        if (formData.lastNameCn && formData.firstNameCn) {
          const fullName = formData.lastNameCn.trim() + formData.firstNameCn.trim();
          try {
            const result = await getPinyin(fullName);
            if (result) {
              // 更新拼音字段和英文姓名
              formData.namePy = result;
              const pinyinParts = result.split(' ');
              if (pinyinParts.length >= 2) {
                formData.lastName = pinyinParts[0].charAt(0).toUpperCase() + pinyinParts[0].slice(1).toLowerCase();
                formData.firstName = pinyinParts.slice(1).map(part => 
                  part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
                ).join(' ');
              }
              await setFieldsValue(formData);
            }
          } catch (error) {
            console.error('获取拼音失败:', error);
          }
        } else if (formData.name && formData.name.trim()) {
          try {
            const result = await getPinyin(formData.name.trim());
            if (result) {
              // 更新拼音字段
              await setFieldsValue({ namePy: result });
            }
          } catch (error) {
            console.error('获取拼音失败:', error);
          }
        }
        
        createMessage.success('信息复制成功');
      } else {
        createMessage.warning('暂无可复制的信息');
    
      }
    } catch (error) {
      console.error('复制信息失败:', error);
      createMessage.error('复制信息失败');
      // copyFromSource.value = false;
    }
  }

  // 重置为默认值
  async function resetToDefaults() {
    try {
      callingCodeValue.value = '+86';
      mobileValue.value = '';
      
      await setFieldsValue({
        lastNameCn: '',
        firstNameCn: '',
        lastName: '',
        firstName: '',
        name: '',
        nameEng: '',
        namePy: '',
        nationality: '',
        idType: '',
        idNumber: '',
        idStartDate: '',
        idEndDate: '',
        idAddress: '',
        contactAddress: '',
        callingCode: '+86',
        mobile: '',
        mobileGroup: '+86 → ',
        occupation: '',
        workDetail: '',
        companyAddress: '',
        email: '',
        bankAccountName: '',
        bankName: '',
        bankCurrency: '',
        bankAccount: '',
        companyName: '',
        trade: '',
        education: '',
        gender: '',
        birthDay: '',
        high: '',
        weight: '',
        izSmoking: '',
        izMarried: '',
        annualIncome: '',
        deposit: '',
        stock: '',
        house: '',
        oldThing: '',
        otherMoney: '',
      });
    } catch (error) {
      console.error('重置表单失败:', error);
    }
  }

  // 标题
  const title = computed(() => {
    const typeText =
      {
        policyHolder: '投保人信息',
        insured: '被保人信息',
        beneficiary: '受益人信息',
      }[unref(personType)] || '个人信息';

    return !unref(isUpdate) ? `添加${typeText}` : `编辑${typeText}`;
  });



  // 根据人员类型获取表单配置
  const getSchemaByType = (type: string) => {
    switch (type) {
      case 'policyHolder':
        return policyHolderFormSchema;
      case 'insured':
        return insuredFormSchema;
      case 'beneficiary':
        return beneficiaryFormSchema;
      default:
        return policyHolderFormSchema;
    }
  };

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: computed(() => getSchemaByType(unref(personType))),
    showActionButtonGroup: false,
  });

  // 注册弹窗
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    personType.value = data?.personType || 'policyHolder';
    
    // 接收客户信息和投保人信息
    customerInfo.value = data?.customerInfo || null;
    policyHolderInfo.value = data?.policyHolderInfo || null;
    


    // 重置错误状态
    mobileError.value = false;

    // 禁用表单验证，等待手动设置值后再启用
    await updateSchema([
      {
        field: 'mobile',
        required: false,
      },
    ]);

    if (unref(isUpdate) && data.record) {
      record.value = { ...data.record };
      // 设置区号和手机号
      callingCodeValue.value = data.record.callingCode || '';
      mobileValue.value = data.record.mobile || '';

      await setFieldsValue({
        ...data.record,
      });
    } else {
      // 新增时默认设置区号为+86
      callingCodeValue.value = '+86';
      mobileValue.value = '';
      await setFieldsValue({
        callingCode: '+86',
        mobile: '',
      });
    }

    // 延迟启用验证
    await nextTick();
    await updateSchema([
      {
        field: 'mobile',
        required: true,
      },
    ]);
  });

  // 提交表单
  async function handleSubmit() {
    try {
      // 手动验证手机号
      if (!mobileValue.value) {
        mobileError.value = true;
        createMessage.error('请输入手机号码');
        return;
      }

      // 先确保表单字段都已正确设置
      await updateFormValues();

      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 处理表单数据
      const formData = {
        ...values,
        callingCode: callingCodeValue.value,
        mobile: mobileValue.value,
      };

      // 如果是受益人，验证受益比例是否在1-100之间
      if (unref(personType) === 'beneficiary' && formData.benefitRatio) {
        const ratio = Number(formData.benefitRatio);
        if (isNaN(ratio) || ratio < 1 || ratio > 100) {
          createMessage.error('受益比例必须在1-100之间');
          setModalProps({ confirmLoading: false });
          return;
        }

        // 添加字典文本
        if (formData.withRelation) {
          const relationDict = getDictItemsByCode('with_relation') || [];
          const dictItem = relationDict.find((item: any) => item.value === formData.withRelation);
          if (dictItem) {
            formData.withRelation_dictText = dictItem.text;
          }
        }
      }

      // 如果是更新操作，需要传入ID
      if (unref(isUpdate) && record.value.id) {
        formData.id = record.value.id;
      }

      closeModal();
      emit('success', { type: unref(personType), data: formData });
      createMessage.success(`操作成功`);
    } catch (error) {
      console.error('表单提交失败:', error);
      createMessage.error('表单验证失败，请检查必填项');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
