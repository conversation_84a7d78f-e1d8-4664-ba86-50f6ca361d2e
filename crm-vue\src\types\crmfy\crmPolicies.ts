// 保单信息类型定义
export interface CrmPolicy {
  id?: number;
  supplierNo?: string;
  productName?: string;
  planName?: string;
  productId?: number;
  paymentTerm?: number;
  paymentFrequency?: string;
  paymentCurrency?: string;
  paymentMethod?: string;
  paymentAmount?: number;
  policyNo?: string;
  coveragePeriod?: string;
  autoDebit?: number;
  promotionPlan?: string;
  notificationEmail?: string;
  signatureSms?: string;
  currentVersion?: number;
  policyStatus?: string;
  orgCode?: string;
  createBy?: string;
  createTime?: Date;
  updateBy?: string;
  updateTime?: Date;
  delFlag?: number;
  tenantId?: number;
  fypExchangeRate?: number;
  rcExchangeRate?: number;
  customerId?: number;
  customerId_dictText?: string;
  customerName?: string;
  salesUsername?: string;
  salesUsername_dictText?: string;
  policyUuid?: string;
  noticeRemark?: string;
  noticeReply?: string;
  izActive?: string;
  nextNode?: string;
  policyIssueDate?: Date;
  coolingOffDate?: Date;
  commissionStatus?: string;
  bookTime?: Date;
  signAddr?: string;
}

// 保单详情DTO
export interface CrmPolicyDTO extends CrmPolicy {
  crmPolicyHolders?: PolicyHolder;
  crmPolicyInsureds?: PolicyInsured;
  crmPolicyBeneficiaries?: PolicyBeneficiary[];
  crmPolicyAdditionals?: PolicyAdditional[];
  crmPolicyFiles?: PolicyFile[];
  izSubmit?: string;
  auditResult?: string;
  changeReason?: string;
}

// 投保人信息
export interface PolicyHolder {
  id?: number;
  policyId?: number;
  version?: number;
  lastNameCn?: string;
  firstNameCn?: string;
  lastName?: string;
  firstName?: string;
  name?: string;
  nameEng?: string;
  namePy?: string;
  nationality?: string;
  idType?: string;
  idNumber?: string;
  idStartDate?: string;
  idEndDate?: string;
  idAddress?: string;
  contactAddress?: string;
  callingCode?: string;
  mobile?: string;
  occupation?: string;
  companyAddress?: string;
  email?: string;
  bankAccountName?: string;
  bankName?: string;
  bankCurrency?: string;
  bankAccount?: string;
  high?: number;
  weight?: number;
  izSmoking?: string;
  izMarried?: string;
  workDetail?: string;
  annualIncome?: number;
  deposit?: number;
  stock?: number;
  house?: number;
  oldThing?: number;
  otherMoney?: number;
}

// 被保人信息
export interface PolicyInsured {
  id?: number;
  policyId?: number;
  version?: number;
  lastNameCn?: string;
  firstNameCn?: string;
  lastName?: string;
  firstName?: string;
  name?: string;
  nameEng?: string;
  namePy?: string;
  nationality?: string;
  idType?: string;
  idNumber?: string;
  idStartDate?: string;
  idEndDate?: string;
  idAddress?: string;
  contactAddress?: string;
  callingCode?: string;
  mobile?: string;
  occupation?: string;
  companyAddress?: string;
  email?: string;
  bankAccountName?: string;
  bankName?: string;
  bankCurrency?: string;
  bankAccount?: string;
  withRelation?: string;
  high?: number;
  weight?: number;
  izSmoking?: string;
  izMarried?: string;
  workDetail?: string;
  annualIncome?: number;
  deposit?: number;
  stock?: number;
  house?: number;
  oldThing?: number;
  otherMoney?: number;
}

// 受益人信息
export interface PolicyBeneficiary {
  id?: number;
  policyId?: number;
  version?: number;
  lastNameCn?: string;
  firstNameCn?: string;
  lastName?: string;
  firstName?: string;
  name?: string;
  nameEng?: string;
  namePy?: string;
  nationality?: string;
  idType?: string;
  idNumber?: string;
  idStartDate?: string;
  idEndDate?: string;
  idAddress?: string;
  contactAddress?: string;
  callingCode?: string;
  mobile?: string;
  occupation?: string;
  companyAddress?: string;
  email?: string;
  bankAccountName?: string;
  bankName?: string;
  bankCurrency?: string;
  bankAccount?: string;
  withRelation?: string;
  withRelation_dictText?: string;
  benefitRatio?: number;
  high?: number;
  weight?: number;
  izSmoking?: string;
  izMarried?: string;
  workDetail?: string;
  annualIncome?: number;
  deposit?: number;
  stock?: number;
  house?: number;
  oldThing?: number;
  otherMoney?: number;
}

// 附加险信息
export interface PolicyAdditional {
  id?: number;
  policyId?: number;
  version?: number;
  productId?: number;
  planName?: string;
  additionalAmount?: number;
}

// 保单文件信息
export interface PolicyFile {
  id?: number;
  policyId?: number;
  version?: number;
  policyNo?: string;
  fileName?: string;
  filePath?: string;
  fileType?: string;
  fileAttr?: string;
  fileSize?: number;
  createBy?: string;
  createTime?: Date;
  fileContentType?: string;
}
