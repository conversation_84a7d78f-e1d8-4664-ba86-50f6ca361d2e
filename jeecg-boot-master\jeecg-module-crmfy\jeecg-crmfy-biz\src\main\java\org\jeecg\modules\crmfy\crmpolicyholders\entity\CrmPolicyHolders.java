package org.jeecg.modules.crmfy.crmpolicyholders.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.desensitization.annotation.SensitiveField;
import org.jeecg.common.desensitization.enums.SensitiveEnum;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 投保人信息
 * @Author: jeecg-boot
 * @Date:   2025-03-28
 * @Version: V1.0
 */
@Data
@TableName("crm_policy_holders")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_policy_holders对象", description="投保人信息")
public class CrmPolicyHolders {
    
	/**id*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
	private java.lang.Integer id;
	/**保单Id*/
	@Excel(name = "保单Id", width = 15)
    @ApiModelProperty(value = "保单Id")
	private java.lang.Integer policyId;
	/**对应版本号*/
	@Excel(name = "对应版本号", width = 15)
    @ApiModelProperty(value = "对应版本号")
	private java.lang.Integer version;
	/**姓*/
	@SensitiveField(type=SensitiveEnum.CHINESE_NAME)
	@Excel(name = "姓", width = 15)
    @ApiModelProperty(value = "姓")
	private java.lang.String lastNameCn;
	/**名*/
	@SensitiveField(type=SensitiveEnum.CHINESE_NAME)
	@Excel(name = "名", width = 15)
    @ApiModelProperty(value = "名")
	private java.lang.String firstNameCn;
	/**英文姓*/
	@Excel(name = "英文姓", width = 15)
    @ApiModelProperty(value = "英文姓")
	private java.lang.String lastName;
	/**英文名*/
	@Excel(name = "英文名", width = 15)
    @ApiModelProperty(value = "英文名")
	private java.lang.String firstName;
	/**姓名*/
	@SensitiveField(type=SensitiveEnum.CHINESE_NAME)
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
	private java.lang.String name;
	/**英文姓名*/
	@Excel(name = "英文姓名", width = 15)
    @ApiModelProperty(value = "英文姓名")
	private java.lang.String nameEng;

	//拼音姓名
	@Excel(name = "拼音姓名", width = 15)
    @ApiModelProperty(value = "拼音姓名")
	private java.lang.String namePy;

	/**国籍*/
	@Excel(name = "国籍", width = 15)
    @ApiModelProperty(value = "国籍")
	private java.lang.String nationality;
	/**证件类型 1身份证，2护照，3其他*/
	@Excel(name = "证件类型 1身份证，2护照，3其他", width = 15)
    @ApiModelProperty(value = "证件类型 1身份证，2护照，3其他")
	private java.lang.String idType;
	/**证件号码*/
	@SensitiveField(type=SensitiveEnum.ID_CARD)
	@Excel(name = "证件号码", width = 15)
    @ApiModelProperty(value = "证件号码")
	private java.lang.String idNumber;
	/**证件有效期起始*/
	@Excel(name = "证件有效期起始", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "证件有效期起始")
	private java.util.Date idStartDate;
	/**证件有效期截止*/
	@Excel(name = "证件有效期截止", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "证件有效期截止")
	private java.util.Date idEndDate;
	/**住宅地址*/
	@SensitiveField(type=SensitiveEnum.ADDRESS)
	@Excel(name = "住宅地址", width = 15)
    @ApiModelProperty(value = "住宅地址")
	private java.lang.String idAddress;
	/**通讯地址*/
	@SensitiveField(type=SensitiveEnum.ADDRESS)
	@Excel(name = "通讯地址", width = 15)
    @ApiModelProperty(value = "通讯地址")
	private java.lang.String contactAddress;

	/**国际区号*/
	@Excel(name = "国际区号", width = 15)
    @ApiModelProperty(value = "国际区号")
	private java.lang.String callingCode;
	
	/**手机号码*/
	@SensitiveField(type=SensitiveEnum.MOBILE_PHONE)
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
	private java.lang.String mobile;
	/**工作职位*/
	@Excel(name = "工作职位", width = 15)
    @ApiModelProperty(value = "工作职位")
	private java.lang.String occupation;
	/**工作内容*/
	@Excel(name = "工作内容", width = 15)
    @ApiModelProperty(value = "工作内容")
	private java.lang.String workDetail;
	/**单位地址*/
	@SensitiveField(type=SensitiveEnum.ADDRESS)
	@Excel(name = "单位地址", width = 15)
    @ApiModelProperty(value = "单位地址")
	private java.lang.String companyAddress;
	/**电子邮箱*/
	@SensitiveField(type=SensitiveEnum.EMAIL)
	@Excel(name = "电子邮箱", width = 15)
    @ApiModelProperty(value = "电子邮箱")
	private java.lang.String email;
	/**银行开户名*/
	@SensitiveField(type=SensitiveEnum.CHINESE_NAME)
	@Excel(name = "银行开户名", width = 15)
    @ApiModelProperty(value = "银行开户名")
	private java.lang.String bankAccountName;
	/**银行名称*/
	@Excel(name = "银行名称", width = 15)
    @ApiModelProperty(value = "银行名称")
	private java.lang.String bankName;
	/**账户币种*/
	@Excel(name = "账户币种", width = 15)
    @ApiModelProperty(value = "账户币种")
	private java.lang.String bankCurrency;
	/**银行账号*/
	@Excel(name = "银行账号", width = 15)
    @ApiModelProperty(value = "银行账号")
	private java.lang.String bankAccount;


	@ApiModelProperty(value = "工作单位")
	private java.lang.String companyName;

	@Dict(dicCode = "trade")
	@ApiModelProperty(value = "行业")
	private java.lang.String trade;

	@Dict(dicCode = "education")
	@ApiModelProperty(value = "教育程度")
	private java.lang.String education;

	@Dict(dicCode = "sex")
	@ApiModelProperty(value = "性别")
	private java.lang.String gender;
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "生日")
	private java.util.Date birthDay;
	/**身高cm*/
	@Excel(name = "身高cm", width = 15)
    @ApiModelProperty(value = "身高cm")
	private java.math.BigDecimal high;
	/**体重kg*/
	@Excel(name = "体重kg", width = 15)
    @ApiModelProperty(value = "体重kg")
	private java.math.BigDecimal weight;
	/**是否吸烟1是 0否*/
	@Excel(name = "是否吸烟1是 0否", width = 15)
    @ApiModelProperty(value = "是否吸烟1是 0否")
	private java.lang.String izSmoking;
	/**是否已婚1是 0否*/
	@Excel(name = "是否已婚1是 0否", width = 15)
    @ApiModelProperty(value = "是否已婚1是 0否")
	private java.lang.String izMarried;
	/**年薪(人民币)*/
	@Excel(name = "年薪(人民币)", width = 15)
    @ApiModelProperty(value = "年薪(人民币)")
	private java.lang.String annualIncome;
	/**存款*/
	@Excel(name = "存款", width = 15)
    @ApiModelProperty(value = "存款")
	private java.lang.String deposit;
	/**股票*/
	@Excel(name = "股票", width = 15)
    @ApiModelProperty(value = "股票")
	private java.lang.String stock;
	/**房产*/
	@Excel(name = "房产", width = 15)
    @ApiModelProperty(value = "房产")
	private java.lang.String house;
	/**古玩字画*/
	@Excel(name = "古玩字画", width = 15)
    @ApiModelProperty(value = "古玩字画")
	private java.lang.String oldThing;
	/**其他资产*/
	@Excel(name = "其他资产", width = 15)
    @ApiModelProperty(value = "其他资产")
	private java.lang.String otherMoney;

	
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
}
