package org.jeecg.modules.crmfy.crmpolicies.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.desensitization.annotation.SensitiveField;
import org.jeecg.common.desensitization.enums.SensitiveEnum;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;


@Data
public class CrmPoliciesVO implements Serializable{

	private static final long serialVersionUID = 1L;

	/**保单ID*/
    @ApiModelProperty(value = "保单ID")
	private java.lang.Integer id;
	/**供应商名称*/
	@Dict(dicCode = "supplier")
	@Excel(name = "供应商", width = 15)
    @ApiModelProperty(value = "供应商")
	private java.lang.String supplierNo;
	/**保险产品名称*/
	@Excel(name = "保险产品名称", width = 15)
    @ApiModelProperty(value = "保险产品名称")
	private java.lang.String productName;
	// /**保险产品编码*/
	// @Excel(name = "保险产品编码", width = 15)
    // @ApiModelProperty(value = "保险产品编码")
	// private java.lang.String productCode;

	@Excel(name = "保险产品名称(英文)", width = 15)
    @ApiModelProperty(value = "保险产品名称(英文)")
	private java.lang.String planName;
	
    @ApiModelProperty(value = "保险产品id")
	private java.lang.Integer productId;

	
	/**缴费年期*/
	@Excel(name = "缴费年期", width = 15)
    @ApiModelProperty(value = "缴费年期")
	private java.lang.Integer paymentTerm;

	/**缴费频率*/
	@Dict(dicCode = "payment_frequency")
	@Excel(name = "缴费频率", width = 15)
    @ApiModelProperty(value = "缴费频率")
	private java.lang.String paymentFrequency;
	/**缴费币种*/
	@Dict(dicCode = "payment_currency")
	@Excel(name = "缴费币种", width = 15)
    @ApiModelProperty(value = "缴费币种")
	private java.lang.String paymentCurrency;
	/**缴费方式*/
	@Dict(dicCode = "payment_method")
	@Excel(name = "缴费方式", width = 15)
    @ApiModelProperty(value = "缴费方式")
	private java.lang.String paymentMethod;
	/**缴费金额*/
	@Excel(name = "缴费金额", width = 15)
    @ApiModelProperty(value = "缴费金额")
	private java.math.BigDecimal paymentAmount;
	/**保单号码*/
	@Excel(name = "保单号码", width = 15)
    @ApiModelProperty(value = "保单号码")
	private java.lang.String policyNo;
	/**保障期限*/
	@Excel(name = "保障期限", width = 15)
    @ApiModelProperty(value = "保障期限")
	private java.lang.String coveragePeriod;
	/**是否自动扣款*/
	@Excel(name = "是否自动扣款", width = 15)
    @ApiModelProperty(value = "是否自动扣款")
	private java.lang.Integer autoDebit;
	/**优惠方案*/
	@Excel(name = "优惠方案", width = 15)
    @ApiModelProperty(value = "优惠方案")
	private java.lang.String promotionPlan;
	/**通知电邮地址*/
	@Excel(name = "通知电邮地址", width = 15)
    @ApiModelProperty(value = "通知电邮地址")
    @SensitiveField(type=SensitiveEnum.EMAIL)
	private java.lang.String notificationEmail;
	/**电子签收链接*/
	@Excel(name = "电子签收链接", width = 15)
    @ApiModelProperty(value = "电子签收链接")
	private java.lang.String signatureSms;
	/**当前数据版本*/
	@Excel(name = "当前数据版本", width = 15)
    @ApiModelProperty(value = "当前数据版本")
	private java.lang.Integer currentVersion;
	/**保单状态*/
	@Dict(dicCode = "policy_status")
	@Excel(name = "保单状态", width = 15)
    @ApiModelProperty(value = "保单状态")
	private java.lang.String policyStatus;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;

	/**缴费日当天汇率*/
	@Excel(name = "缴费日当天汇率", width = 15)
    @ApiModelProperty(value = "缴费日当天汇率")
	private java.math.BigDecimal fypExchangeRate;

	@Excel(name = "佣金结算币种", width = 15)
    @ApiModelProperty(value = "佣金结算币种")
	private String commissionCurrency;
	/**佣金结算日当天汇率*/
	@Excel(name = "佣金结算日当天汇率", width = 15)
    @ApiModelProperty(value = "佣金结算日当天汇率")
	private java.math.BigDecimal rcExchangeRate;


	@ApiModelProperty(value = "客户ID")
	private java.lang.Integer customerId;

    @ApiModelProperty(value = "客户姓名")
    @SensitiveField(type=SensitiveEnum.CHINESE_NAME)
	private java.lang.String customerName;

	/**客户编码*/
	@Excel(name = "客户编号", width = 15)
    @ApiModelProperty(value = "客户编号")
	private java.lang.String customerNo;




	@Dict(dictTable = "sys_user",dicText = "realname",dicCode = "username")
	@Excel(name = "销售员", width = 15,dictTable = "sys_user",dicText = "realname",dicCode = "username")
	@ApiModelProperty(value = "销售经理账号")
	private java.lang.String salesUsername;

	@ApiModelProperty(value = "保单唯一标识")
	private java.lang.String policyUuid;

	/**照会说明*/
	@Excel(name = "照会说明", width = 15)
    @ApiModelProperty(value = "照会说明")
	private java.lang.String noticeRemark;

	@ApiModelProperty(value = "照会回复-")
	private java.lang.String noticeRePly;

	@ApiModelProperty(value = "是否有效：0-历史版本 1-当前版本")
	private java.lang.String izActive;

	@ApiModelProperty(value = "下一个节点流程 状态：1-保单提交 2-运营审核 3-核保回复 4-照会回复 5-保费到账确认 6-保单签发")
	private java.lang.String nextNode;
	/**预约时间*/
	@Excel(name = "预约时间", width = 15)
    @ApiModelProperty(value = "预约时间")
	private java.util.Date bookTime;
	
	/**签单地址*/
	@Excel(name = "签单地址", width = 15)
    @ApiModelProperty(value = "签单地址")
	private java.lang.String signAddr;

		@ApiModelProperty(value = "客户信息")
	private org.jeecg.modules.crmfy.crmcustomer.entity.CrmCustomer newCustomerInfo;

}
