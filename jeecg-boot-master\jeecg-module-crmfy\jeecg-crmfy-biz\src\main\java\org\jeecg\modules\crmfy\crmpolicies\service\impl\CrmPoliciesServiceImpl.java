package org.jeecg.modules.crmfy.crmpolicies.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;
import java.util.Base64;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.CommonSendStatus;
import org.jeecg.common.constant.WebsocketConst;
import org.jeecg.common.desensitization.annotation.SensitiveEncode;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.crmfy.crmcommissioncalculation.entity.CrmCommissionCalculation;
import org.jeecg.modules.crmfy.crmcommissioncalculation.mapper.CrmCommissionCalculationMapper;
import org.jeecg.modules.crmfy.crmcommissioncalculation.service.ICrmCommissionCalculationService;
import org.jeecg.modules.crmfy.crmcommissioncalculation.service.impl.CrmCommissionCalculationServiceImpl;
import org.jeecg.modules.crmfy.crmpolicies.entity.CrmPolicies;
import org.jeecg.modules.crmfy.crmpolicies.mapper.CrmPoliciesMapper;
import org.jeecg.modules.crmfy.crmpolicies.service.ICrmPoliciesService;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesDTO;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesPO;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesVO;
import org.jeecg.modules.crmfy.crmpolicyadditional.entity.CrmPolicyAdditional;
import org.jeecg.modules.crmfy.crmpolicyadditional.mapper.CrmPolicyAdditionalMapper;
import org.jeecg.modules.crmfy.crmpolicybeneficiaries.entity.CrmPolicyBeneficiaries;
import org.jeecg.modules.crmfy.crmpolicybeneficiaries.mapper.CrmPolicyBeneficiariesMapper;
import org.jeecg.modules.crmfy.crmpolicyfiles.entity.CrmPolicyFiles;
import org.jeecg.modules.crmfy.crmpolicyfiles.mapper.CrmPolicyFilesMapper;
import org.jeecg.modules.crmfy.crmpolicyholders.entity.CrmPolicyHolders;
import org.jeecg.modules.crmfy.crmpolicyholders.mapper.CrmPolicyHoldersMapper;
import org.jeecg.modules.crmfy.crmpolicyinsureds.entity.CrmPolicyInsureds;
import org.jeecg.modules.crmfy.crmpolicyinsureds.mapper.CrmPolicyInsuredsMapper;
import org.jeecg.modules.crmfy.crmpolicyversions.entity.CrmPolicyVersions;
import org.jeecg.modules.crmfy.crmpolicyversions.mapper.CrmPolicyVersionsMapper;
import org.jeecg.modules.crmfy.crmproductcontractrate.entity.CrmProductContractRate;
import org.jeecg.modules.crmfy.crmproductcontractrate.mapper.CrmProductContractRateMapper;
import org.jeecg.modules.crmfy.enums.PolicyStatusEnum;
import org.jeecg.modules.jsjx.enums.PostEnum;
import org.jeecg.modules.jsjx.utils.DataUtils;
import org.jeecg.modules.jsjx.utils.MyDateUtils;
import org.jeecg.modules.message.websocket.WebSocket;
import org.jeecg.modules.system.entity.SysAnnouncement;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysAnnouncementService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.utils.IpUtils;
import org.springframework.beans.factory.annotation.Value;

import org.jeecg.modules.crmfy.crmcustomer.entity.CrmCustomer;
import org.jeecg.modules.crmfy.crmcustomer.service.ICrmCustomerService;
import org.jeecg.modules.crmfy.crmpolicypay.entity.CrmPolicyPay;
import org.jeecg.modules.crmfy.crmpolicypay.mapper.CrmPolicyPayMapper;

/**
 * @Description: 保单信息管理
 * @Author: jeecg-boot
 * @Date: 2025-03-28
 * @Version: V1.0
 */
@Slf4j
@Service
public class CrmPoliciesServiceImpl extends ServiceImpl<CrmPoliciesMapper, CrmPolicies> implements ICrmPoliciesService {

	@Autowired
	private CrmPolicyHoldersMapper policyHoldersMapper;

	@Autowired
	private CrmPolicyInsuredsMapper policyInsuredsMapper;

	@Autowired
	private CrmPolicyBeneficiariesMapper policyBeneficiariesMapper;

	@Autowired
	private CrmPolicyVersionsMapper policyVersionsMapper;

	@Autowired
	private ISysUserService sysUserService;

	@Autowired
	private ISysAnnouncementService sysAnnouncementService;

	@Autowired
	private CrmPolicyAdditionalMapper crmPolicyAdditionalMapper;

	@Autowired
	private CrmPolicyFilesMapper policyFilesMapper;

	@Autowired
	private CrmProductContractRateMapper crmProductContractRateMapper;

	@Autowired
	private CrmCommissionCalculationMapper crmCommissionCalculationMapper;

	@Autowired
	private ICrmCommissionCalculationService crmCommissionCalculationService;

	@Autowired
	private ISysBaseAPI sysBaseAPI;

	@Autowired
	private ICrmCustomerService crmCustomerService;

	@Autowired
	private CrmPolicyPayMapper crmPolicyPayMapper;

	@Resource
	private WebSocket webSocket;

	@Value("${crmfy.signatureUrl}")
	private String signatureIPPortUrl;

	@SensitiveEncode
	@Override
	public CrmPoliciesDTO queryCrmPoliciesByIdEncode(String id) {
		if (StringUtils.isBlank(id)) {
			throw new JeecgBootException("参数错误");
		}

		// 查询保单主表信息
		CrmPolicies crmPolicies = this.getById(id);
		if (crmPolicies == null) {
			throw new JeecgBootException("保单不存在");
		}

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();

		if (post.equals(PostEnum.XSKHJL.getCode()) && !loginUser.getUsername().equals(crmPolicies.getSalesUsername())) {// 转介人
			throw new JeecgBootException("非法操作，无权限！");
		}

		// 创建返回对象
		CrmPoliciesDTO crmPoliciesDTO = new CrmPoliciesDTO();
		BeanUtils.copyProperties(crmPolicies, crmPoliciesDTO);

		// 查询投保人信息
		CrmPolicyHolders policyHolders = policyHoldersMapper.selectOne(
				new QueryWrapper<CrmPolicyHolders>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyHolders(policyHolders);

		// 查询被保人信息
		CrmPolicyInsureds policyInsureds = policyInsuredsMapper.selectOne(
				new QueryWrapper<CrmPolicyInsureds>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyInsureds(policyInsureds);

		// 查询受益人信息
		List<CrmPolicyBeneficiaries> policyBeneficiariesList = policyBeneficiariesMapper.selectList(
				new QueryWrapper<CrmPolicyBeneficiaries>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyBeneficiaries(policyBeneficiariesList);

		// 查询附加险信息
		List<CrmPolicyAdditional> crmPolicyAdditionals = crmPolicyAdditionalMapper.selectList(
				new QueryWrapper<CrmPolicyAdditional>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyAdditionals(crmPolicyAdditionals);

		// 查询订单文件
		List<CrmPolicyFiles> files = policyFilesMapper.selectList(
				new QueryWrapper<CrmPolicyFiles>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyFiles(files);

		return crmPoliciesDTO;
	}

	@Override
	public CrmPoliciesDTO queryCrmPoliciesById(String id) {
		if (StringUtils.isBlank(id)) {
			throw new JeecgBootException("参数错误");
		}

		// 查询保单主表信息
		CrmPolicies crmPolicies = this.getById(id);
		if (crmPolicies == null) {
			throw new JeecgBootException("保单不存在");
		}
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();

		if (post.equals(PostEnum.XSKHJL.getCode()) && !loginUser.getUsername().equals(crmPolicies.getSalesUsername())) {// 转介人
			throw new JeecgBootException("非法操作，无权限！");
		}
		// 创建返回对象
		CrmPoliciesDTO crmPoliciesDTO = new CrmPoliciesDTO();
		BeanUtils.copyProperties(crmPolicies, crmPoliciesDTO);

		// 查询投保人信息
		CrmPolicyHolders policyHolders = policyHoldersMapper.selectOne(
				new QueryWrapper<CrmPolicyHolders>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyHolders(policyHolders);

		// 查询被保人信息
		CrmPolicyInsureds policyInsureds = policyInsuredsMapper.selectOne(
				new QueryWrapper<CrmPolicyInsureds>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyInsureds(policyInsureds);

		// 查询受益人信息
		List<CrmPolicyBeneficiaries> policyBeneficiariesList = policyBeneficiariesMapper.selectList(
				new QueryWrapper<CrmPolicyBeneficiaries>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyBeneficiaries(policyBeneficiariesList);

		// 查询附加险信息
		List<CrmPolicyAdditional> crmPolicyAdditionals = crmPolicyAdditionalMapper.selectList(
				new QueryWrapper<CrmPolicyAdditional>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyAdditionals(crmPolicyAdditionals);

		// 查询订单文件
		List<CrmPolicyFiles> files = policyFilesMapper.selectList(
				new QueryWrapper<CrmPolicyFiles>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyFiles(files);

		return crmPoliciesDTO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveCrmPolicies(CrmPoliciesDTO crmPoliciesDTO) {

		// 1、根据CrmPoliciesDTO 保存主表CrmPolicies，2、根据CrmPoliciesDTO
		// 保存子表CrmPolicyHolders，3、根据CrmPoliciesDTO
		// 保存子表CrmPolicyInsureds，4、根据CrmPoliciesDTO 保存子表CrmPolicyBeneficiaries
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 1. 保存主表CrmPolicies
		CrmPolicies crmPolicies = new CrmPolicies();
		// 设置主表数据
		crmPolicies.setSupplierNo(crmPoliciesDTO.getSupplierNo());
		crmPolicies.setProductName(crmPoliciesDTO.getProductName());
		crmPolicies.setPlanName(crmPoliciesDTO.getPlanName());
		crmPolicies.setProductId(crmPoliciesDTO.getProductId());
		crmPolicies.setPaymentTerm(crmPoliciesDTO.getPaymentTerm());
		crmPolicies.setPaymentFrequency(crmPoliciesDTO.getPaymentFrequency());
		crmPolicies.setPaymentCurrency(crmPoliciesDTO.getPaymentCurrency());
		crmPolicies.setPaymentMethod(crmPoliciesDTO.getPaymentMethod());
		crmPolicies.setPaymentAmount(crmPoliciesDTO.getPaymentAmount());
		crmPolicies.setPolicyNo(crmPoliciesDTO.getPolicyNo());
		crmPolicies.setCoveragePeriod(crmPoliciesDTO.getCoveragePeriod());
		crmPolicies.setAutoDebit(crmPoliciesDTO.getAutoDebit());
		crmPolicies.setPromotionPlan(crmPoliciesDTO.getPromotionPlan());
		crmPolicies.setNotificationEmail(crmPoliciesDTO.getNotificationEmail());
		crmPolicies.setSignatureSms(crmPoliciesDTO.getSignatureSms());
		crmPolicies.setCurrentVersion(1); // 初始版本为1
		crmPolicies.setCustomerId(crmPoliciesDTO.getCustomerId());
		crmPolicies.setDelFlag(0);
		crmPolicies.setSalesUsername(loginUser.getUsername());
		crmPolicies.setOrgCode(loginUser.getOrgCode());
		// CrmPolicyBeneficiaries crmPolicyBeneficiaries =
		// crmPoliciesDTO.getCrmPolicyBeneficiaries();
		CrmPolicyInsureds crmPolicyInsureds = crmPoliciesDTO.getCrmPolicyInsureds();
		CrmPolicyHolders crmPolicyHolders = crmPoliciesDTO.getCrmPolicyHolders();
		List<CrmPolicyAdditional> crmPolicyAdditionals = crmPoliciesDTO.getCrmPolicyAdditionals();
		List<CrmPolicyFiles> crmPolicyFiles = crmPoliciesDTO.getCrmPolicyFiles();
		crmPolicies.setPolicyUuid(java.util.UUID.randomUUID().toString());

		if (crmPoliciesDTO.getIzSubmit().equals("0")) {
			crmPolicies.setNextNode("1");// 提交
			crmPolicies.setPolicyStatus(PolicyStatusEnum.SIGNED_SAVE.getStatus());// 草稿
		} else {
			crmPolicies.setNextNode("2");// 运营审核
			crmPolicies.setPolicyStatus(PolicyStatusEnum.SIGNED_PENDING_SUBMIT.getStatus());// 已签单待提交
		}
		// 保存主表数据
		this.save(crmPolicies);

		// 获取保存后的主表ID
		Integer policyId = crmPolicies.getId();

		// 2. 保存子表CrmPolicyHolders（投保人信息）
		if (crmPolicyHolders != null) {
			// 设置关联的保单ID和版本号
			crmPolicyHolders.setPolicyId(policyId);
			crmPolicyHolders.setVersion(1); // 初始版本为1
			crmPolicyHolders.setDelFlag(0);
			crmPolicyHolders.setName(crmPolicyHolders.getLastNameCn() + crmPolicyHolders.getFirstNameCn());
			// 保存投保人信息
			policyHoldersMapper.insert(crmPolicyHolders);
		}

		// 3. 保存子表CrmPolicyInsureds（被保人信息）
		if (crmPolicyInsureds != null) {
			// 设置关联的保单ID和版本号
			crmPolicyInsureds.setPolicyId(policyId);
			crmPolicyInsureds.setVersion(1); // 初始版本为1
			crmPolicyInsureds.setDelFlag(0); // 设置删除标记为0，未删除状态，默认值为0，可根据需求进行修改或删除该属性或注释掉这行代码
			crmPolicyInsureds.setName(crmPolicyInsureds.getLastNameCn() + crmPolicyInsureds.getFirstNameCn());
			// 保存被保人信息
			policyInsuredsMapper.insert(crmPolicyInsureds);
		}

		// 4. 保存子表CrmPolicyBeneficiaries（受益人信息列表）
		List<CrmPolicyBeneficiaries> beneficiariesList = crmPoliciesDTO.getCrmPolicyBeneficiaries();
		if (beneficiariesList != null && !beneficiariesList.isEmpty()) {
			for (CrmPolicyBeneficiaries beneficiary : beneficiariesList) {
				// 设置关联的保单ID和版本号
				beneficiary.setId(null);
				beneficiary.setPolicyId(policyId);
				beneficiary.setVersion(1); // 初始版本为1
				beneficiary.setDelFlag(0); // 设置删除标记为0
				beneficiary.setName(beneficiary.getLastNameCn() + beneficiary.getFirstNameCn());
				// 保存受益人信息
				policyBeneficiariesMapper.insert(beneficiary);
			}
		}

		// 保存附加险信息
		if (crmPolicyAdditionals != null && crmPolicyAdditionals.size() > 0) {
			for (CrmPolicyAdditional crmPolicyAdditional : crmPolicyAdditionals) {
				crmPolicyAdditional.setPolicyId(policyId);
				crmPolicyAdditional.setVersion(1); // 初始版本为1
				crmPolicyAdditional.setDelFlag(0);
				crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
			}
		}
		// 保存订单文件
		if (crmPolicyFiles != null && crmPolicyFiles.size() > 0) {
			for (CrmPolicyFiles crmPolicyFile : crmPolicyFiles) {
				// 保存文件信息
				crmPolicyFile.setId(null);
				crmPolicyFile.setPolicyId(policyId);
				crmPolicyFile.setVersion(1); // 初始版本为1
				crmPolicyFile.setDelFlag(0);
				policyFilesMapper.insert(crmPolicyFile);
			}
		}

		// 5. 创建保单版本记录
		CrmPolicyVersions crmPolicyVersions = new CrmPolicyVersions();
		crmPolicyVersions.setPolicyId(policyId);
		crmPolicyVersions.setVersion(1); // 初始版本为1
		crmPolicyVersions.setHolderId(crmPolicyHolders != null ? crmPolicyHolders.getId() : null);
		crmPolicyVersions.setInsuredId(crmPolicyInsureds != null ? crmPolicyInsureds.getId() : null);
		crmPolicyVersions.setBeneficiaryId(
				beneficiariesList != null && !beneficiariesList.isEmpty() ? beneficiariesList.get(0).getId() : null);
		// 对于多个受益人，我们不再在版本记录中保存受益人ID
		crmPolicyVersions.setEffectiveDate(new Date()); // 设置当前日期为生效日期
		crmPolicyVersions.setDelFlag(0);
		crmPolicyVersions.setPolicyUuid(crmPolicies.getPolicyUuid());
		crmPolicyVersions.setIzActive("1");
		// 保存版本记录
		policyVersionsMapper.insert(crmPolicyVersions);

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCrmPoliciesByIdSales(CrmPoliciesDTO crmPoliciesDTO) {
		if (crmPoliciesDTO == null || crmPoliciesDTO.getId() == null) {
			throw new JeecgBootException("参数错误");
		}
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();
		if (StringUtils.isBlank(post)) {
			throw new JeecgBootException("操作失败，请先分配职务");
		}
		// 1. 获取当前保单信息
		CrmPolicies currentPolicy = this.getById(crmPoliciesDTO.getId());
		if (currentPolicy == null) {
			throw new JeecgBootException("保单不存在");
		}

		if (post.equals(PostEnum.XSKHJL.getCode())) {// 转介人

			if (!loginUser.getUsername().equals(currentPolicy.getSalesUsername())) {// 转介人只能修改自己的保单
				throw new JeecgBootException("非法操作，无权限！");
			}

			if (!currentPolicy.getNextNode().equals("1") && !currentPolicy.getNextNode().equals("4")) {// 下个节点是提交/照会回复
				throw new JeecgBootException("当前保单状态不允许修改");
			}

			CrmPolicies crmPolicies = new CrmPolicies();
			BeanUtils.copyProperties(crmPoliciesDTO, crmPolicies);
			crmPolicies.setId(crmPoliciesDTO.getId());
			crmPolicies.setUpdateBy(loginUser.getUsername());
			crmPolicies.setUpdateTime(new Date());

			if (currentPolicy.getNextNode().equals("1")) {// 下个节点是提交

				if (crmPoliciesDTO.getIzSubmit().equals("1")) {
					crmPolicies.setNextNode("2");// 运营审核
					crmPolicies.setPolicyStatus(PolicyStatusEnum.SIGNED_PENDING_SUBMIT.getStatus());// 已签单待提交
				}

			} else if (currentPolicy.getNextNode().equals("4")) {// 下个节点是照会回复
				currentPolicy.setNoticeReply(crmPoliciesDTO.getNoticeReply());
				crmPolicies.setNextNode("3");// 核保回复
				crmPolicies.setPolicyStatus(PolicyStatusEnum.INQUIRY_REPLIED_PENDING_UNDERWRITING.getStatus());// 照会已回复待核保

				// 发送通知给运营岗
				SysAnnouncement sysAnnouncement = new SysAnnouncement();
				sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);
				sysAnnouncement.setSendTime(new Date());
				sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
				sysAnnouncement.setSender(loginUser.getUsername());
				sysAnnouncement.setTitile("照会回复通知");
				sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);
				// 通过username获取userId
				SysUser user = sysUserService.getUserByName(currentPolicy.getUpdateBy());
				sysAnnouncement.setUserIds(user.getId());
				// 通过customerId获取客户姓名

				CrmCustomer crmCustomer = crmCustomerService.getById(currentPolicy.getCustomerId());

				if (crmCustomer != null) {
					sysAnnouncement.setMsgContent("客户：" + crmCustomer.getCustomerName() + "，的照会已回复，回复内容："
							+ crmPolicies.getNoticeReply() + "，请尽快处理。");
					sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);
					sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);
					sysAnnouncement.setMsgAbstract("照会回复通知");
					// 保存系统公告
					sysAnnouncementService.saveAnnouncement(sysAnnouncement);
					// 发送webSocket通知
					String userId = sysAnnouncement.getUserIds();
					// String[] userIds = userId.substring(0, (userId.length() - 1)).split(",");
					JSONObject obj = new JSONObject();
					obj.put(WebsocketConst.MSG_CMD, WebsocketConst.CMD_USER);
					obj.put(WebsocketConst.MSG_ID, sysAnnouncement.getId());
					obj.put(WebsocketConst.MSG_TXT, sysAnnouncement.getTitile());
					webSocket.sendMessage(userId, obj.toJSONString());

				}
			}

			List<CrmPolicyBeneficiaries> crmPolicyBeneficiaries = crmPoliciesDTO.getCrmPolicyBeneficiaries();
			CrmPolicyInsureds crmPolicyInsureds = crmPoliciesDTO.getCrmPolicyInsureds();
			CrmPolicyHolders crmPolicyHolders = crmPoliciesDTO.getCrmPolicyHolders();
			List<CrmPolicyAdditional> crmPolicyAdditionals = crmPoliciesDTO.getCrmPolicyAdditionals();
			List<CrmPolicyFiles> crmPolicyFiles = crmPoliciesDTO.getCrmPolicyFiles();

			// 更新主表数据
			this.updateById(crmPolicies);

			// 2. 修改子表CrmPolicyHolders（投保人信息）
			if (crmPolicyHolders != null) {
				if (crmPolicyHolders.getId() == null) {
					// 设置关联的保单ID和版本号
					crmPolicyHolders.setPolicyId(crmPolicies.getId());
					crmPolicyHolders.setVersion(1); // 初始版本为1
					crmPolicyHolders.setDelFlag(0);
					crmPolicyHolders.setName(crmPolicyHolders.getLastNameCn() + crmPolicyHolders.getFirstNameCn());
					// 保存投保人信息
					policyHoldersMapper.insert(crmPolicyHolders);
				} else {
					// 修改投保人信息
					policyHoldersMapper.updateById(crmPolicyHolders);
				}

			}

			// 3. 修改子表CrmPolicyInsureds（被保人信息）
			if (crmPolicyInsureds != null) {
				if (crmPolicyInsureds.getId() == null) {
					// 设置关联的保单ID和版本号
					crmPolicyInsureds.setPolicyId(crmPolicies.getId());
					crmPolicyInsureds.setVersion(1); // 初始版本为1
					crmPolicyInsureds.setDelFlag(0);
					crmPolicyInsureds.setName(crmPolicyInsureds.getLastNameCn() + crmPolicyInsureds.getFirstNameCn());
					// 保存被保人信息
					policyInsuredsMapper.insert(crmPolicyInsureds);
				} else {
					// 修改被保人信息
					policyInsuredsMapper.updateById(crmPolicyInsureds);
				}

			}

			// 4. 修改子表CrmPolicyBeneficiaries（受益人信息）
			if (crmPolicyBeneficiaries != null && crmPolicyBeneficiaries.size() > 0) {
				// 先删除 后新增
				// 删除原受益人信息
				policyBeneficiariesMapper.delete(new LambdaQueryWrapper<CrmPolicyBeneficiaries>()
						.eq(CrmPolicyBeneficiaries::getPolicyId, crmPoliciesDTO.getId()));
				// 新增受益人信息
				for (CrmPolicyBeneficiaries crmPolicyBeneficiary : crmPolicyBeneficiaries) {
					// 设置关联的保单ID和版本号
					crmPolicyBeneficiary.setPolicyId(crmPolicies.getId());
					crmPolicyBeneficiary.setVersion(currentPolicy.getCurrentVersion()); // 初始版本为1
					crmPolicyBeneficiary.setDelFlag(0);
					crmPolicyBeneficiary.setId(null);
					crmPolicyBeneficiary
							.setName(crmPolicyBeneficiary.getLastNameCn() + crmPolicyBeneficiary.getFirstNameCn());
					// 保存受益人信息
					policyBeneficiariesMapper.insert(crmPolicyBeneficiary);
				}

			}

			// 修改附加险信息
			if (crmPolicyAdditionals != null && crmPolicyAdditionals.size() > 0) {
				// 先删除 后新增
				// 删除原附加信息

				crmPolicyAdditionalMapper.delete(new LambdaQueryWrapper<CrmPolicyAdditional>()
						.eq(CrmPolicyAdditional::getPolicyId, crmPoliciesDTO.getId()));
				for (CrmPolicyAdditional crmPolicyAdditional : crmPolicyAdditionals) {
					crmPolicyAdditional.setVersion(currentPolicy.getCurrentVersion());
					crmPolicyAdditional.setDelFlag(0);
					crmPolicyAdditional.setPolicyId(crmPoliciesDTO.getId());
					crmPolicyAdditional.setId(null);
					crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
				}
			}

			// 修改订单文件信息
			if (crmPolicyFiles != null && crmPolicyFiles.size() > 0) {
				// 先删除 后新增
				// 删除原文件信息
				policyFilesMapper.delete(new LambdaQueryWrapper<CrmPolicyFiles>().eq(CrmPolicyFiles::getPolicyId,
						crmPoliciesDTO.getId()));
				for (CrmPolicyFiles crmPolicyFile : crmPolicyFiles) {
					crmPolicyFile.setVersion(currentPolicy.getCurrentVersion());
					crmPolicyFile.setDelFlag(0);
					crmPolicyFile.setPolicyId(crmPoliciesDTO.getId());
					crmPolicyFile.setId(null);
					policyFilesMapper.insert(crmPolicyFile);
				}
			}

		} else {
			throw new JeecgBootException("当前用户不允许操作");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCrmPoliciesById(CrmPoliciesDTO crmPoliciesDTO) {

		if (crmPoliciesDTO == null || crmPoliciesDTO.getId() == null) {
			throw new JeecgBootException("参数错误");
		}

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();
		if (StringUtils.isBlank(post)) {
			throw new JeecgBootException("操作失败，请先分配职务");

		}

		// 1. 获取当前保单信息
		CrmPolicies currentPolicy = this.getById(crmPoliciesDTO.getId());
		if (currentPolicy == null) {
			throw new JeecgBootException("保单不存在");
		}

		if (post.equals(PostEnum.YYRY.getCode())) {// 运营岗
			// if (post.equals(PostEnum.XSKHJL.getCode())
			// &&
			// !PolicyStatusEnum.SIGNED_SAVE.getStatus().equals(currentPolicy.getPolicyStatus()))
			// {// 转介人
			// throw new JeecgBootException("当前保单状态不允许修改");
			// }

			List<CrmPolicyBeneficiaries> crmPolicyBeneficiaries = crmPoliciesDTO.getCrmPolicyBeneficiaries();
			CrmPolicyInsureds crmPolicyInsureds = crmPoliciesDTO.getCrmPolicyInsureds();
			CrmPolicyHolders crmPolicyHolders = crmPoliciesDTO.getCrmPolicyHolders();
			List<CrmPolicyAdditional> crmPolicyAdditionals = crmPoliciesDTO.getCrmPolicyAdditionals();
			List<CrmPolicyFiles> crmPolicyFiles = crmPoliciesDTO.getCrmPolicyFiles();
			// 3. 更新主表CrmPolicies数据
			CrmPolicies crmPolicies = new CrmPolicies();
			BeanUtils.copyProperties(crmPoliciesDTO, crmPolicies);
			crmPolicies.setId(crmPoliciesDTO.getId());
			if (!PolicyStatusEnum.ORDER_COMPLETED.getStatus().equals(currentPolicy.getPolicyStatus())) {// 订单完成前可直接修改

				if (currentPolicy.getNextNode().equals("2")) {// 下个节点是运营岗审核提交

					crmPolicies.setPolicyStatus(PolicyStatusEnum.SUBMITTED_PENDING_UNDERWRITING.getStatus());// 已提交待核保
					crmPolicies.setNextNode("3");// 核保回复

				} else if (currentPolicy.getNextNode().equals("3")) {// 下个节点是运营岗核保回复

					if (crmPoliciesDTO.getAuditResult().equals("1")) {// 核保通过
						crmPolicies.setNextNode("5");// 保费到账
						crmPolicies.setPolicyStatus(PolicyStatusEnum.UNDERWRITTEN_PENDING_PAYMENT.getStatus());// 核保通过待保费到账
					} else {// 核保不通过
						crmPolicies.setNextNode("4");// 照会回复
						crmPolicies.setPolicyStatus(PolicyStatusEnum.INQUIRY_PENDING_PROCESSING.getStatus());// 照会待处理
						crmPolicies.setNoticeRemark(crmPoliciesDTO.getNoticeRemark());

						// 发送通知给转介人
						SysAnnouncement sysAnnouncement = new SysAnnouncement();
						sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);
						sysAnnouncement.setSendTime(new Date());
						sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
						sysAnnouncement.setSender(loginUser.getUsername());
						sysAnnouncement.setTitile("照会通知");
						sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);
						// 通过username获取userId
						SysUser user = sysUserService.getUserByName(currentPolicy.getSalesUsername());
						sysAnnouncement.setUserIds(user.getId());
						sysAnnouncement
								.setMsgContent("尊敬的客户：您的核保未通过，" + crmPolicies.getNoticeRemark() + "，请按照相关规定进行处理。");
						sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);
						sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);
						sysAnnouncement.setMsgAbstract("照会通知");

						// 保存系统公告
						sysAnnouncementService.saveAnnouncement(sysAnnouncement);
						// 发送webSocket通知
						String userId = sysAnnouncement.getUserIds();
						// String[] userIds = userId.substring(0, (userId.length() - 1)).split(",");
						JSONObject obj = new JSONObject();
						obj.put(WebsocketConst.MSG_CMD, WebsocketConst.CMD_USER);
						obj.put(WebsocketConst.MSG_ID, sysAnnouncement.getId());
						obj.put(WebsocketConst.MSG_TXT, sysAnnouncement.getTitile());
						webSocket.sendMessage(userId, obj.toJSONString());
					}

				} else if (currentPolicy.getNextNode().equals("4")) {// 下个节点是照会回复
					currentPolicy.setNoticeReply(crmPoliciesDTO.getNoticeReply());
					crmPolicies.setNextNode("3");// 核保回复
					crmPolicies.setPolicyStatus(PolicyStatusEnum.INQUIRY_REPLIED_PENDING_UNDERWRITING.getStatus());// 照会已回复待核保

					// 发送通知给运营岗
					SysAnnouncement sysAnnouncement = new SysAnnouncement();
					sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);
					sysAnnouncement.setSendTime(new Date());
					sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
					sysAnnouncement.setSender(loginUser.getUsername());
					sysAnnouncement.setTitile("照会回复通知");
					sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);
					// 通过username获取userId
					SysUser user = sysUserService.getUserByName(currentPolicy.getUpdateBy());
					sysAnnouncement.setUserIds(user.getId());
					// 通过customerId获取客户姓名

					CrmCustomer crmCustomer = crmCustomerService.getById(currentPolicy.getCustomerId());
					if (crmCustomer != null) {
						sysAnnouncement.setMsgContent("客户：" + crmCustomer.getCustomerName() + "，的照会已回复，回复内容："
								+ crmPolicies.getNoticeReply() + "，请尽快处理。");
						sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);
						sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);
						sysAnnouncement.setMsgAbstract("照会回复通知");
						// 保存系统公告
						sysAnnouncementService.saveAnnouncement(sysAnnouncement);
						// 发送webSocket通知
						String userId = sysAnnouncement.getUserIds();
						// String[] userIds = userId.substring(0, (userId.length() - 1)).split(",");
						JSONObject obj = new JSONObject();
						obj.put(WebsocketConst.MSG_CMD, WebsocketConst.CMD_USER);
						obj.put(WebsocketConst.MSG_ID, sysAnnouncement.getId());
						obj.put(WebsocketConst.MSG_TXT, sysAnnouncement.getTitile());
						webSocket.sendMessage(userId, obj.toJSONString());

					}

				} else if (currentPolicy.getNextNode().equals("5")) {// 下个节点是保费到账确认
					crmPolicies.setNextNode("6");// 保单签发
					crmPolicies.setPolicyStatus(PolicyStatusEnum.PAID_PENDING_ISSUANCE.getStatus());// 保费到账待签发保单

				} else if (currentPolicy.getNextNode().equals("6")) {// 下个节点是保单签发
					crmPolicies.setNextNode("7");// 保单签发
					crmPolicies.setPolicyStatus(PolicyStatusEnum.POLICY_ISSUED.getStatus());// 保单签发
					// Date date = new Date();
					// crmPolicies.setPolicyIssueDate(date);// 保单签发日期
					// crmPolicies.setCoolingOffDate(MyDateUtils.getExpectDate(date, 21));// 保单生效日期

					// 生成签字链接
					generateSignatureLink(crmPolicies, crmPoliciesDTO.getSignatureIpAndPort());
				} else if (currentPolicy.getNextNode().equals("7")) {// 下个节点是保单完成
					if (currentPolicy.getCoolingOffDate().compareTo(new Date()) > 0) {
						throw new JeecgBootException("保单尚未过冷静期，不能完成！");
					}
					crmPolicies.setNextNode("8");// 保单流程结束
					crmPolicies.setPolicyStatus(PolicyStatusEnum.ORDER_COMPLETED.getStatus());// 保单完成
					// 计算保单每期佣金
					// 获取销售员佣金费率
					SysUser sysUser = sysUserService.getUserByName(currentPolicy.getSalesUsername());
					BigDecimal commissionRate = sysUser.getCommissionRate();
					if (commissionRate == null) {
						throw new JeecgBootException("请先设置销售员佣金费率！");
					}
					commissionRate = commissionRate.divide(new BigDecimal(100));
					// 获取保单对应合约手续费率
					List<CrmProductContractRate> crmProductContractRates = crmProductContractRateMapper
							.selectList(new LambdaQueryWrapper<CrmProductContractRate>()
									.eq(CrmProductContractRate::getProductId, currentPolicy.getProductId())
									.eq(CrmProductContractRate::getPaymentTerm, currentPolicy.getPaymentTerm())
									.orderByAsc(CrmProductContractRate::getYearSeq));
					Date date = new Date();
					if (crmProductContractRates.size() > 0) {
						List<CrmCommissionCalculation> crmCommissionCalculations = new ArrayList<CrmCommissionCalculation>();
						// 遍历
						for (CrmProductContractRate crmProductContractRate : crmProductContractRates) {
							// 计算保单每期主险佣金
							BigDecimal commissionAmount = BigDecimal.ZERO;
							if (currentPolicy.getPaymentTerm() < 5) {
								commissionAmount = currentPolicy
										.getPaymentAmount().multiply(crmProductContractRate.getContractRate()
												.divide(new BigDecimal(100)).subtract(commissionRate))
										.setScale(2, BigDecimal.ROUND_HALF_UP);
							} else {
								commissionAmount = currentPolicy.getPaymentAmount()
										.multiply(crmProductContractRate.getContractRate()).divide(new BigDecimal(100))
										.multiply(commissionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
							}
							// 保存佣金到佣金计算表
							CrmCommissionCalculation crmCommissionCalculation = new CrmCommissionCalculation();
							crmCommissionCalculation.setPolicyId(currentPolicy.getId());
							crmCommissionCalculation.setPlanName(currentPolicy.getPlanName());
							crmCommissionCalculation.setPolicyNo(crmPoliciesDTO.getPolicyNo());
							crmCommissionCalculation.setRateId(crmProductContractRate.getId());
							crmCommissionCalculation.setYearNum(crmProductContractRate.getYearSeq());
							crmCommissionCalculation.setPremiumAmount(currentPolicy.getPaymentAmount());
							crmCommissionCalculation.setCommissionRate(commissionRate);
							crmCommissionCalculation.setCommissionAmount(commissionAmount);
							crmCommissionCalculation.setCompanyPayoutRatio(crmProductContractRate.getContractRate());
							crmCommissionCalculation.setContractRate(crmProductContractRate.getContractRate());
							// crmCommissionCalculation.setCompanyFee(crmProductContractRate.getCompanyFee());
							crmCommissionCalculation.setPaymentCurrency(currentPolicy.getPaymentCurrency());
							crmCommissionCalculation.setCalculationDate(date);
							crmCommissionCalculation.setPayoutStatus("0");
							crmCommissionCalculation.setSysOrgCode(loginUser.getOrgCode());
							crmCommissionCalculation.setSaleName(currentPolicy.getSalesUsername());
							crmCommissionCalculation.setDelFlag(0);
							crmCommissionCalculation.setPayoutDate(
									MyDateUtils.getExpectYear(date, crmProductContractRate.getYearSeq() - 1));
							crmCommissionCalculation.setCompanyCode(currentPolicy.getSupplierNo());
							crmCommissionCalculation.setRcExchangeRate(currentPolicy.getRcExchangeRate());
							crmCommissionCalculations.add(crmCommissionCalculation);

						}

						// 计算每期附加险佣金
						List<CrmPolicyAdditional> additionals = crmPolicyAdditionalMapper
								.selectList(new LambdaQueryWrapper<CrmPolicyAdditional>()
										.eq(CrmPolicyAdditional::getPolicyId, currentPolicy.getId()));
						if (additionals.size() > 0) {
							for (CrmPolicyAdditional crmPolicyAdditional : additionals) {

								// 获取保单对应合约手续费率
								List<CrmProductContractRate> crmProductContractRates2 = crmProductContractRateMapper
										.selectList(new LambdaQueryWrapper<CrmProductContractRate>()
												.eq(CrmProductContractRate::getProductId,
														crmPolicyAdditional.getProductId())
												.eq(CrmProductContractRate::getPaymentTerm,
														currentPolicy.getPaymentTerm())
												.orderByAsc(CrmProductContractRate::getYearSeq));

								for (CrmProductContractRate crmProductContractRate2 : crmProductContractRates2) {
									// 计算每期附加险佣金
									BigDecimal additionalCommissionAmount = BigDecimal.ZERO;
									if (currentPolicy.getPaymentTerm() < 5) {
										additionalCommissionAmount = crmPolicyAdditional.getAdditionalAmount()
												.multiply(crmProductContractRate2.getContractRate()
														.divide(new BigDecimal(100)).subtract(commissionRate))
												.setScale(2, BigDecimal.ROUND_HALF_UP);
									} else {
										additionalCommissionAmount = crmPolicyAdditional.getAdditionalAmount()
												.multiply(crmProductContractRate2.getContractRate())
												.divide(new BigDecimal(100)).multiply(commissionRate)
												.setScale(2, BigDecimal.ROUND_HALF_UP);
									}
									// 保存佣金到佣金计算表
									CrmCommissionCalculation crmCommissionCalculationAdditional = new CrmCommissionCalculation();
									crmCommissionCalculationAdditional.setPolicyId(currentPolicy.getId());
									crmCommissionCalculationAdditional.setPolicyNo(crmPoliciesDTO.getPolicyNo());
									crmCommissionCalculationAdditional.setPlanName(crmPolicyAdditional.getPlanName());
									crmCommissionCalculationAdditional.setRateId(crmProductContractRate2.getId());
									crmCommissionCalculationAdditional.setYearNum(crmProductContractRate2.getYearSeq());
									crmCommissionCalculationAdditional
											.setPremiumAmount(crmPolicyAdditional.getAdditionalAmount());
									crmCommissionCalculationAdditional.setCommissionRate(commissionRate);
									crmCommissionCalculationAdditional.setCommissionAmount(additionalCommissionAmount);
									crmCommissionCalculationAdditional
											.setContractRate(crmProductContractRate2.getContractRate());
									crmCommissionCalculationAdditional
											.setCompanyPayoutRatio(crmProductContractRate2.getContractRate());
									// crmCommissionCalculationAdditional.setCompanyFee(crmProductContractRate.getCompanyFee());
									crmCommissionCalculationAdditional
											.setPaymentCurrency(currentPolicy.getPaymentCurrency());
									crmCommissionCalculationAdditional.setCalculationDate(date);
									crmCommissionCalculationAdditional.setPayoutStatus("0");
									crmCommissionCalculationAdditional.setSysOrgCode(loginUser.getOrgCode());
									crmCommissionCalculationAdditional.setSaleName(sysUser.getUsername());
									crmCommissionCalculationAdditional.setDelFlag(0);
									crmCommissionCalculationAdditional.setPayoutDate(
											MyDateUtils.getExpectYear(date, crmProductContractRate2.getYearSeq() - 1));
									crmCommissionCalculationAdditional.setCompanyCode(currentPolicy.getSupplierNo());

									crmCommissionCalculations.add(crmCommissionCalculationAdditional);

								}

							}

						}

						crmCommissionCalculationService.saveBatch(crmCommissionCalculations);
					} else {
						throw new JeecgBootException("请先设置保单产品对应合约手续费率！");
					}

					// 生成每期缴费数据 crmPolicyPay
					// for (int i = 0; i < currentPolicy.getPaymentTerm(); i++) {
					// CrmPolicyPay crmPolicyPay = new CrmPolicyPay();
					// crmPolicyPay.setPolicyId(crmPoliciesDTO.getId());
					// crmPolicyPay.setPolicyNo(crmPoliciesDTO.getPolicyNo());
					// crmPolicyPay.setVersion(currentPolicy.getCurrentVersion());
					// if(i==currentPolicy.getPaymentTerm()-1){
					// crmPolicyPay.setIzPay(1);
					// crmPolicyPay.setPayDate(date);
					// }else{
					// crmPolicyPay.setIzPay(0);
					// crmPolicyPay.setPayDate(null);
					// }
					// crmPolicyPay.setPaymentAmount(currentPolicy.getPaymentAmount());
					// crmPolicyPay.setYearNum(i+1);

					// crmPolicyPay.setCreateBy(loginUser.getUsername());
					// crmPolicyPay.setCreateTime(date);
					// crmPolicyPay.setUpdateBy(loginUser.getUsername());
					// crmPolicyPay.setUpdateTime(date);
					// crmPolicyPay.setDelFlag(0);
					// crmPolicyPayMapper.insert(crmPolicyPay);
					// }

					// todo 更新客户vip等级信息

				} else {
					throw new JeecgBootException("当前保单状态不允许操作");
				}
				// 更新主表数据
				this.updateById(crmPolicies);

				// 2. 修改子表CrmPolicyHolders（投保人信息）
				if (crmPolicyHolders != null) {
					// 修改投保人信息
					policyHoldersMapper.updateById(crmPolicyHolders);
				}

				// 3. 修改子表CrmPolicyInsureds（被保人信息）
				if (crmPolicyInsureds != null) {
					// 修改被保人信息
					policyInsuredsMapper.updateById(crmPolicyInsureds);
				}

				// 4. 修改子表CrmPolicyBeneficiaries（受益人信息）
				if (crmPolicyBeneficiaries != null && crmPolicyBeneficiaries.size() > 0) {// 先删除 后新增
					// 删除原受益人信息
					policyBeneficiariesMapper.delete(new LambdaQueryWrapper<CrmPolicyBeneficiaries>()
							.eq(CrmPolicyBeneficiaries::getPolicyId, crmPoliciesDTO.getId()));
					// 新增受益人信息
					for (CrmPolicyBeneficiaries crmPolicyBeneficiary : crmPolicyBeneficiaries) {
						crmPolicyBeneficiary.setPolicyId(crmPolicies.getId());
						crmPolicyBeneficiary.setVersion(currentPolicy.getCurrentVersion());
						crmPolicyBeneficiary.setDelFlag(0);
						crmPolicyBeneficiary.setId(null);
						// 保存受益人信息
						policyBeneficiariesMapper.insert(crmPolicyBeneficiary);
					}
				}

				// 先删除 后新增
				// 删除原附加信息

				crmPolicyAdditionalMapper.delete(new LambdaQueryWrapper<CrmPolicyAdditional>()
						.eq(CrmPolicyAdditional::getPolicyId, crmPoliciesDTO.getId()));
				for (CrmPolicyAdditional crmPolicyAdditional : crmPolicyAdditionals) {
					crmPolicyAdditional.setVersion(currentPolicy.getCurrentVersion());
					crmPolicyAdditional.setDelFlag(0);
					crmPolicyAdditional.setPolicyId(crmPoliciesDTO.getId());
					crmPolicyAdditional.setId(null);
					crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
				}
				// 删除原文件信息
				policyFilesMapper.delete(new LambdaQueryWrapper<CrmPolicyFiles>().eq(CrmPolicyFiles::getPolicyId,
						crmPoliciesDTO.getId()));
				// 新增文件信息
				for (CrmPolicyFiles crmPolicyFile : crmPolicyFiles) {
					crmPolicyFile.setVersion(currentPolicy.getCurrentVersion());
					crmPolicyFile.setDelFlag(0);
					crmPolicyFile.setPolicyId(crmPoliciesDTO.getId());
					crmPolicyFile.setId(null);
					policyFilesMapper.insert(crmPolicyFile);
				}

			} else {// 订单完成后不能直接修改

				// 更新主表记录为历史版本
				CrmPolicies policy = new CrmPolicies();
				policy.setId(crmPoliciesDTO.getId());
				policy.setIzActive("0");
				policy.setUpdateTime(new Date());
				policy.setUpdateBy(loginUser.getUsername());
				this.updateById(policy);

				crmPolicies.setCurrentVersion(currentPolicy.getCurrentVersion() + 1); // 版本号加1
				crmPolicies.setId(null);
				crmPolicies.setUpdateTime(new Date());
				crmPolicies.setUpdateBy(loginUser.getUsername());
				crmPolicies.setPolicyUuid(currentPolicy.getPolicyUuid());
				crmPolicies.setIzActive("1");
				crmPolicies.setDelFlag(0);

				// 保存主表数据
				this.save(crmPolicies);

				// 更新历史版本为失效状态
				policyVersionsMapper.update(null, new UpdateWrapper<CrmPolicyVersions>()
						.eq("policy_uuid", crmPolicies.getPolicyUuid())
						.set("iz_active", "0"));

				// 5. 创建保单版本记录
				CrmPolicyVersions crmPolicyVersions = new CrmPolicyVersions();
				crmPolicyVersions.setPolicyId(crmPolicies.getId());
				crmPolicyVersions.setVersion(crmPolicies.getCurrentVersion()); // 当前版本号
				crmPolicyVersions.setHolderId(crmPolicyHolders != null ? crmPolicyHolders.getId() : null);
				crmPolicyVersions.setInsuredId(crmPolicyInsureds != null ? crmPolicyInsureds.getId() : null);
				crmPolicyVersions.setBeneficiaryId(crmPolicyBeneficiaries != null && crmPolicyBeneficiaries.size() > 0
						? crmPolicyBeneficiaries.get(0).getId()
						: null);
				crmPolicyVersions.setEffectiveDate(new Date()); // 设置当前日期为生效日期
				crmPolicyVersions.setDelFlag(0);
				crmPolicyVersions.setPolicyUuid(crmPolicies.getPolicyUuid());
				crmPolicyVersions.setIzActive("1");
				crmPolicyVersions.setChangeReason(crmPoliciesDTO.getChangeReason());
				// 保存版本记录
				policyVersionsMapper.insert(crmPolicyVersions);

				// 4. 更新投保人信息
				if (crmPolicyHolders != null) {
					crmPolicyHolders.setPolicyId(crmPolicies.getId());
					crmPolicyHolders.setVersion(crmPolicies.getCurrentVersion());
					crmPolicyHolders.setDelFlag(0);
					policyHoldersMapper.insert(crmPolicyHolders);
				}

				// 5. 更新被保人信息
				if (crmPolicyInsureds != null) {
					crmPolicyInsureds.setPolicyId(crmPolicies.getId());
					crmPolicyInsureds.setVersion(crmPolicies.getCurrentVersion());
					crmPolicyInsureds.setDelFlag(0);
					policyInsuredsMapper.insert(crmPolicyInsureds);
				}

				// 6. 更新受益人信息
				if (crmPolicyBeneficiaries != null && crmPolicyBeneficiaries.size() > 0) {

					// policyBeneficiariesMapper.delete(new
					// LambdaQueryWrapper<CrmPolicyBeneficiaries>().eq(CrmPolicyBeneficiaries::getPolicyId,
					// crmPoliciesDTO.getId()));
					// 新增受益人信息
					for (CrmPolicyBeneficiaries crmPolicyBeneficiary : crmPolicyBeneficiaries) {
						crmPolicyBeneficiary.setPolicyId(crmPolicies.getId());
						crmPolicyBeneficiary.setVersion(crmPolicies.getCurrentVersion());
						crmPolicyBeneficiary.setDelFlag(0);
						crmPolicyBeneficiary.setId(null);
						// 保存受益人信息
						policyBeneficiariesMapper.insert(crmPolicyBeneficiary);
					}
				}
				// 更新附加险信息
				if (crmPolicyAdditionals != null && crmPolicyAdditionals.size() > 0) {
					for (CrmPolicyAdditional crmPolicyAdditional : crmPolicyAdditionals) {
						crmPolicyAdditional.setVersion(crmPolicies.getCurrentVersion());
						crmPolicyAdditional.setDelFlag(0);
						crmPolicyAdditional.setPolicyId(crmPolicies.getId());
						crmPolicyAdditional.setId(null);
						crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
					}
				}
				// 更新文件信息
				if (crmPolicyFiles != null && crmPolicyFiles.size() > 0) {

					// policyFilesMapper.delete(new
					// LambdaQueryWrapper<CrmPolicyFiles>().eq(CrmPolicyFiles::getPolicyId,
					// crmPoliciesDTO.getId()));
					// 新增文件信息
					for (CrmPolicyFiles crmPolicyFile : crmPolicyFiles) {
						crmPolicyFile.setVersion(crmPolicies.getCurrentVersion());
						crmPolicyFile.setDelFlag(0);
						crmPolicyFile.setPolicyId(crmPolicies.getId());
						crmPolicyFile.setId(null);
						policyFilesMapper.insert(crmPolicyFile);
					}
				}

			}

		} else {
			throw new JeecgBootException("当前用户不允许操作");
		}

	}

	public boolean sendAnnouncement(String username, SysAnnouncement sysAnnouncement) {
		return false;
	}

	@SensitiveEncode(entity = CrmPoliciesVO.class)
	@Override
	public IPage<CrmPoliciesVO> queryPoliciesInfoEncode(Page<CrmPoliciesVO> page, CrmPoliciesPO crmPoliciesPO) {
		// 调用Mapper层执行分页查询
		return baseMapper.queryPoliciesInfo(page, crmPoliciesPO);
	}

	@Override
	public IPage<CrmPoliciesVO> queryPoliciesInfo(Page<CrmPoliciesVO> page, CrmPoliciesPO crmPoliciesPO) {
		// 调用Mapper层执行分页查询
		return baseMapper.queryPoliciesInfo(page, crmPoliciesPO);
	}

	@Override
	public Map<String, Object> getSignatureInfo(String token) {
		Map<String, Object> result = new HashMap<>();

		try {
			// 解析token获取保单ID（这里简化处理，实际应该有加密解密逻辑）
			String policyIdStr = new String(Base64.getDecoder().decode(token));
			Integer policyId = Integer.valueOf(policyIdStr);

			// 获取保单信息
			CrmPolicies policy = this.getById(policyId);
			if (policy == null) {
				throw new JeecgBootException("保单不存在");
			}

			// 检查保单状态是否允许签字或查看签字信息
			if (!"7".equals(policy.getPolicyStatus()) && !"8".equals(policy.getPolicyStatus())
					&& !"9".equals(policy.getPolicyStatus())) {
				throw new JeecgBootException("保单状态不允许签字");
			}

			// 获取投保人信息
			CrmPolicyHolders policyHolder = policyHoldersMapper.selectOne(
					new LambdaQueryWrapper<CrmPolicyHolders>()
							.eq(CrmPolicyHolders::getPolicyId, policyId)
							.eq(CrmPolicyHolders::getVersion, policy.getCurrentVersion())
							.eq(CrmPolicyHolders::getDelFlag, 0));

			// 获取被保人信息
			CrmPolicyInsureds insured = policyInsuredsMapper.selectOne(
					new LambdaQueryWrapper<CrmPolicyInsureds>()
							.eq(CrmPolicyInsureds::getPolicyId, policyId)
							.eq(CrmPolicyInsureds::getVersion, policy.getCurrentVersion())
							.eq(CrmPolicyInsureds::getDelFlag, 0));

			// 获取保单文件（只获取保单文件类型）
			CrmPolicyFiles policyFile = policyFilesMapper.selectOne(
					new LambdaQueryWrapper<CrmPolicyFiles>()
							.eq(CrmPolicyFiles::getPolicyId, policyId)
							.eq(CrmPolicyFiles::getFileType, "1") // 1表示保单文件
							.eq(CrmPolicyFiles::getVersion, policy.getCurrentVersion())
							.eq(CrmPolicyFiles::getDelFlag, 0));

			// 构建保单信息
			Map<String, Object> policyInfo = new HashMap<>();
			policyInfo.put("policyNo", policy.getPolicyNo());
			policyInfo.put("productName", policy.getProductName());
			policyInfo.put("paymentAmount", policy.getPaymentAmount());
			policyInfo.put("paymentCurrency", policy.getPaymentCurrency());
			policyInfo.put("paymentFrequency", policy.getPaymentFrequency());
			policyInfo.put("policyHolderName", policyHolder != null ? policyHolder.getName() : "");
			policyInfo.put("insuredName", insured != null ? insured.getName() : "");

			result.put("policyInfo", policyInfo);
			result.put("policyFile", policyFile);

			// 检查是否已经完成签字
			boolean signatureCompleted = policy.getSignatureStatus() != null && policy.getSignatureStatus().equals("1");
			result.put("signatureCompleted", signatureCompleted);

			// 如果已经签字，返回签字图片和签字时间
			if (signatureCompleted) {
				result.put("signatureImg", policy.getSignatureImg());
				result.put("signatureTime", policy.getSignatureTime());
			}

			log.info("获取保单 {} 签字信息成功，签字状态: {}", policyId, signatureCompleted);

		} catch (NumberFormatException e) {
			throw new JeecgBootException("签字链接格式错误");
		} catch (IllegalArgumentException e) {
			throw new JeecgBootException("签字链接无效");
		} catch (Exception e) {
			log.error("获取签字信息失败", e);
			throw new JeecgBootException("获取签字信息失败：" + e.getMessage());
		}

		return result;
	}

	@Override
	public void submitSignature(Map<String, Object> signatureData) {
		String token = (String) signatureData.get("token");
		String signatureDataUrl = (String) signatureData.get("signatureData");
		String signatureTime = (String) signatureData.get("signatureTime");

		try {
			// 解析token获取保单ID
			String policyIdStr = new String(Base64.getDecoder().decode(token));
			Integer policyId = Integer.valueOf(policyIdStr);

			// 获取保单信息
			CrmPolicies policy = this.getById(policyId);
			if (policy == null) {
				throw new JeecgBootException("保单不存在");
			}

			// 检查保单状态是否允许签字
			if (!"7".equals(policy.getPolicyStatus())) {
				throw new JeecgBootException("保单状态不允许签字");
			}

			// 这里可以保存签字图片到文件系统或数据库
			// 暂时记录签字时间到备注字段或新增签字状态字段
			log.info("保单 " + policyId + " 完成电子签字，签字时间: " + signatureTime);
			log.info("签字数据长度: " + (signatureDataUrl != null ? signatureDataUrl.length() : 0));

			// 可以在这里添加签字完成的业务逻辑，比如更新保单状态等
			policy.setSignatureStatus("1");
			policy.setSignatureTime(new Date());
			policy.setSignatureImg(signatureDataUrl);
			this.updateById(policy);

		} catch (Exception e) {
			throw new JeecgBootException("提交签字失败：" + e.getMessage());
		}
	}

	@Override
	public void sendSignatureEmail(Map<String, Object> emailData) {
		Integer policyId = (Integer) emailData.get("policyId");
		String email = (String) emailData.get("email");
		String customerName = (String) emailData.get("customerName");

		try {
			// 获取保单信息
			CrmPolicies policy = this.getById(policyId);
			if (policy == null) {
				throw new JeecgBootException("保单不存在");
			}

			// 检查是否已有签字链接
			String signatureUrl = policy.getSignatureSms();
			if (StringUtils.isBlank(signatureUrl)) {
				// 如果没有签字链接，生成一个
				String token = Base64.getEncoder().encodeToString(policyId.toString().getBytes());
				signatureUrl = signatureIPPortUrl + "?token=" + token;
				policy.setSignatureSms(signatureUrl);
				policy.setUpdateTime(new Date());
				this.updateById(policy);
			}

			// 构建邮件内容
			String emailTitle = "保单电子签字通知 - " + policy.getProductName();
			String emailContent = buildSignatureEmailContent(policy, signatureUrl, customerName);

			// 发送邮件
			sysBaseAPI.sendEmailMsg(email, emailTitle, emailContent);

			log.info("已向客户 " + customerName + " (" + email + ") 发送保单签字邮件");

		} catch (Exception e) {
			throw new JeecgBootException("发送签字邮件失败：" + e.getMessage());
		}
	}

	/**
	 * 生成签字链接
	 * 
	 * @param policy    保单信息
	 * @param ipAndPort 签字IP端口
	 */
	private void generateSignatureLink(CrmPolicies policy, String ipAndPort) {
		try {
			// 检查是否有保单文件
			List<CrmPolicyFiles> policyFiles = policyFilesMapper.selectList(
					new LambdaQueryWrapper<CrmPolicyFiles>()
							.eq(CrmPolicyFiles::getPolicyId, policy.getId())
							.eq(CrmPolicyFiles::getFileType, "1") // 1表示保单文件
							.eq(CrmPolicyFiles::getDelFlag, 0));

			if (policyFiles != null && !policyFiles.isEmpty()) {
				// 生成签字token（简化处理，实际应该有更安全的加密方式）
				String token = Base64.getEncoder().encodeToString(policy.getId().toString().getBytes());

				// 构建签字链接

				String signatureUrl = signatureIPPortUrl + "?token=" + token;

				// 保存签字链接到保单
				policy.setSignatureSms(signatureUrl);

				log.info("为保单 " + policy.getId() + " 生成签字链接: " + signatureUrl);
			} else {
				log.warn("保单 " + policy.getId() + " 没有保单文件，无法生成签字链接");
			}
		} catch (Exception e) {
			log.error("生成签字链接失败", e);
		}
	}

	/**
	 * 构建签字邮件内容
	 * 
	 * @param policy       保单信息
	 * @param signatureUrl 签字链接
	 * @param customerName 客户姓名
	 * @return 邮件内容
	 */
	private String buildSignatureEmailContent(CrmPolicies policy, String signatureUrl, String customerName) {
		StringBuilder content = new StringBuilder();
		content.append("<html><body>");
		content.append("<h2>保单电子签字通知</h2>");
		content.append("<p>尊敬的 ").append(customerName).append(" 先生/女士：</p>");
		content.append("<p>您好！您的保单已经签发完成，请点击以下链接进行电子签字确认：</p>");
		content.append("<div style='margin: 20px 0;'>");
		content.append("<h3>保单信息：</h3>");
		content.append("<ul>");
		content.append("<li>保单号：").append(policy.getPolicyNo() != null ? policy.getPolicyNo() : "待生成").append("</li>");
		content.append("<li>产品名称：").append(policy.getProductName()).append("</li>");
		content.append("<li>保费金额：").append(policy.getPaymentAmount()).append("(" + policy.getPaymentCurrency() + ")")
				.append("</li>");
		content.append("<li>签发日期：")
				.append(policy.getPolicyIssueDate() != null ? policy.getPolicyIssueDate().toString() : "")
				.append("</li>");
		content.append("</ul>");
		content.append("</div>");
		content.append(
				"<div style='margin: 20px 0; padding: 15px; background-color: #f0f8ff; border: 1px solid #1890ff; border-radius: 5px;'>");
		content.append("<p><strong>请点击以下链接进行电子签字：</strong></p>");
		content.append("<p><a href='").append(signatureUrl)
				.append("' target='_blank' style='color: #1890ff; text-decoration: none; font-size: 16px;'>")
				.append(signatureUrl).append("</a></p>");
		content.append("</div>");
		content.append("<div style='margin: 20px 0; color: #666; font-size: 14px;'>");
		content.append("<p><strong>温馨提示：</strong></p>");
		content.append("<ul>");
		content.append("<li>请在收到邮件后尽快完成签字确认</li>");
		content.append("<li>如有任何疑问，请联系您的保险顾问</li>");
		content.append("<li>此邮件为系统自动发送，请勿回复</li>");
		content.append("</ul>");
		content.append("</div>");
		content.append("<p>谢谢！</p>");
		content.append("<p>凡亚保险客服团队</p>");
		content.append("</body></html>");
		return content.toString();
	}

}
